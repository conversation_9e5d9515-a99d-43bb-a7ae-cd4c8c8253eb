# 测试修复后的ChatDoc类
from langchain_community.document_loaders import UnstructuredExcelLoader,Docx2txtLoader, PyPDFLoader
from langchain_text_splitters import CharacterTextSplitter 
from langchain_chroma import Chroma 
from langchain_openai import OpenAIEmbeddings 
from langchain.retrievers.multi_query import MultiQueryRetriever
import os
from langchain_deepseek import ChatDeepSeek
from langchain.retrievers import ContextualCompressionRetriever
from langchain.retrievers.document_compressors import LLMChainExtractor

# 使用国产模型
embeddings_model = OpenAIEmbeddings(model="BAAI/bge-m3",api_key="sk-vgbdlqjvfytxctphzbbdzcfzzqhpmrjhjynftgnpdkchbjfd",base_url="https://api.siliconflow.cn/v1")

llm = ChatDeepSeek(
    model="deepseek-chat",
    temperature=0,
    api_key="***********************************",
    base_url="https://api.deepseek.com/v1"
)

# 定义修复后的ChatDoc类
class ChatDoc():
    def __init__(self):
        self.doc = None
        self.splitText = []
        self.file_path = None

    def getFile(self):
        doc = self.doc
        if not doc:
            print("错误：未设置文档路径")
            return None
            
        loaders = {
            "docx":Docx2txtLoader,
            "pdf":PyPDFLoader,
            "xlsx":UnstructuredExcelLoader,
        }
        file_extension = doc.split(".")[-1]
        loader_class = loaders.get(file_extension)
        if loader_class:
            try:
                loader = loader_class(doc)
                text = loader.load()
                print(f"成功加载 {file_extension} 文件，共 {len(text)} 个文档片段")
                return text
            except Exception as e:
                print(f"Error loading {file_extension} files:{e}")
                return None
        else:
            print(f"Unsupported file extension: {file_extension}")
            return None
    
    # 分割文本
    def splitSentence(self):
        full_text = self.getFile()
        if full_text != None:
            text_splitter = CharacterTextSplitter(chunk_size=150, chunk_overlap=20)
            texts = text_splitter.split_documents(full_text)
            self.splitText = texts
            print(f"文本分割完成，共 {len(texts)} 个片段")
            # 打印前几个片段的内容用于调试
            for i, text in enumerate(texts[:3]):
                print(f"片段 {i+1}: {text.page_content[:100]}...")
        else:
            print("文件加载失败")

    def getSplitText(self):
        return self.splitText
    
    #向量化向量存贮
    def embeddingAmdVectorDB(self):
        if not self.splitText:
            print("错误：没有分割的文本数据")
            return None
        try:
            db = Chroma.from_documents(self.splitText,embeddings_model)
            print("向量数据库创建成功")
            return db
        except Exception as e:
            print(f"向量化失败: {e}")
            return None
    
    # 向量化
    def embedding(self):
        return embeddings_model.embed_documents(self.splitText)
    
    # 向量检索
    def vectorSearch(self,query,k=5):
        db = self.embeddingAmdVectorDB()
        if db:
            results = db.similarity_search(query,k)
            print(f"基础向量检索找到 {len(results)} 个相关片段")
            return results
        return []
    
    #提问并找到相关文本快，使用压缩检索
    def askANdFindFiles(self,query):
        db = self.embeddingAmdVectorDB()
        if not db:
            print("向量数据库创建失败")
            return []
            
        try:
            # 先尝试基础检索看看是否有结果
            basic_results = db.similarity_search(query, k=5)
            print(f"基础检索找到 {len(basic_results)} 个相关片段")
            
            if not basic_results:
                print("基础检索没有找到相关内容，请检查查询词是否存在于文档中")
                return []
            
            # 创建上下文压缩检索 - 修复参数名
            retriever = db.as_retriever()
            compressor = LLMChainExtractor.from_llm(llm=llm)
            compressed_retriever = ContextualCompressionRetriever(
                base_retriever=retriever,
                base_compressor=compressor,  # 修复：使用正确的参数名
            )
            results = compressed_retriever.invoke(query)
            print(f"压缩检索找到 {len(results)} 个相关片段")
            return results
            
        except Exception as e:
            print(f"检索过程出错: {e}")
            # 如果压缩检索失败，返回基础检索结果
            return self.vectorSearch(query)

if __name__ == "__main__":
    # 测试修复后的代码
    print("=== 测试修复后的ChatDoc类 ===")
    
    chat_doc = ChatDoc()
    chat_doc.doc = "test.xlsx"
    print("=== 开始处理文档 ===")
    chat_doc.splitSentence()

    print("\n=== 测试不同的查询 ===")

    # 先测试一个更通用的查询
    print("1. 测试通用查询：")
    unique_doc = chat_doc.askANdFindFiles("请假")
    print(f"结果数量: {len(unique_doc)}")
    for i, doc in enumerate(unique_doc[:2]):
        print(f"结果 {i+1}: {doc.page_content[:200]}...")

    print("\n2. 测试原始查询：")
    unique_doc = chat_doc.askANdFindFiles("找到唐欢的数据")
    print(f"结果数量: {len(unique_doc)}")
    if unique_doc:
        for i, doc in enumerate(unique_doc):
            print(f"结果 {i+1}: {doc.page_content[:200]}...")
    else:
        print("没有找到相关数据") 