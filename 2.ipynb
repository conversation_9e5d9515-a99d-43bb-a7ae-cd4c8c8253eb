{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting docx2txt==0.8\n", "  Downloading docx2txt-0.8.tar.gz (2.8 kB)\n", "  Preparing metadata (setup.py) ... \u001b[?25ldone\n", "\u001b[?25hBuilding wheels for collected packages: docx2txt\n", "  Building wheel for docx2txt (setup.py) ... \u001b[?25ldone\n", "\u001b[?25h  Created wheel for docx2txt: filename=docx2txt-0.8-py3-none-any.whl size=3959 sha256=a7ef0483ebd6399c72d9de3ff95a06dbed15ae839c2bc65bfa791d0bef16d794\n", "  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/6f/f7/05/c745e7756faa8641660b6159b979deb122f2e3e1e0d9287eeb\n", "Successfully built docx2txt\n", "Installing collected packages: docx2txt\n", "Successfully installed docx2txt-0.8\n"]}], "source": ["! pip install docx2txt==0.8"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'source': 'test.docx'}, page_content='变更历史 \\n\\n版本号\\n\\n日期\\n\\n作者\\n\\n扼要说明\\n\\nV0.1.1\\n\\n2024-04-24\\n\\n胡欣\\n\\n新建文档，新增1.1、1.2、2.1接口描述\\n\\n1校验结果查询相关接口\\n\\n1.1校验结果列表接口\\n\\nURI\\n\\nGET /validation/api/external/results/list\\n\\n功能\\n\\n获取校验结果列表\\n\\n\\n\\n参数名\\n\\n参数含义\\n\\n类型\\n\\n必选\\n\\nURL参数\\n\\n\\ttaskCode\\n\\n\\t校验规则编号\\n\\n\\tstring\\n\\n\\t是\\n\\n\\n\\n\\tstartExecTime\\n\\n\\t执行时间开始时间(yyyy-MM-dd HH:mm:ss)\\n\\n\\tdate\\n\\n\\t否\\n\\n\\n\\n\\tendExecTime\\n\\n\\t执行时间结束时间(yyyy-MM-dd HH:mm:ss)\\n\\n\\tdate\\n\\n\\t否\\n\\n\\n\\n\\tpreconditionIssueType\\n\\n\\t前置条件问题类型\\n\\n\\tstring\\n\\n\\t否\\n\\n\\n\\n\\tissueType\\n\\n\\t问题类型\\n\\n\\tstring\\n\\n\\t否\\n\\n\\n\\n\\tpage\\n\\n\\t分页数（默认0）\\n\\n\\tint\\n\\n\\t否\\n\\n\\n\\n\\tsize\\n\\n\\t分页大小(默认10)\\n\\n\\tint\\n\\n\\t否\\n\\n请求示例\\n\\nhttp://192.168.2.26:8051/validation/api/external/results/list?startExecTime=2024-03-11%2012:34:33&endExecTime=2024-03-13%2012:34:34&preconditionIssueType=GZBZQ&taskCode=BSZNJY000055\\n\\n返回\\n\\n结果\\n\\n参数\\n\\n参数名\\n\\n参数含义\\n\\n\\n\\ncode\\n\\n调用结果：\\n\\n10000-成功\\n\\n10002-失败\\n\\n\\n\\nmsg\\n\\n查询结果描述\\n\\n\\n\\ndata\\n\\n查询结果\\n\\n\\n\\nstate\\n\\n响应状态，0-失败，1-成功\\n\\n返回\\n\\n成功\\n\\n示例\\n\\n{\\n\\n    \"data\": {\\n\\n        \"content\": [\\n\\n            {\\n\\n                \"taskCode\": \"BSZNJY000055-0004\",\\n\\n                \"taskName\": \"测试222\",\\n\\n                \"executionTime\": \"2024-03-12 12:34:33\",\\n\\n                \"issueFields\": [\\n\\n                    {\\n\\n                        \"enName\": null,\\n\\n                        \"zhName\": \"数学111\",\\n\\n                        \"type\": null,\\n\\n                        \"preconditionIssue\": \"该值不准确\",\\n\\n                        \"amount\": \"1\"\\n\\n                    }\\n\\n                ],\\n\\n                \"total\": 1,\\n\\n                \"issueTotal\": 1,\\n\\n                \"successTotal\": 0\\n\\n            }\\n\\n        ],\\n\\n        \"pageable\": {\\n\\n            \"sort\": {\\n\\n                \"sorted\": true,\\n\\n                \"unsorted\": false,\\n\\n                \"empty\": false\\n\\n            },\\n\\n            \"pageNumber\": 0,\\n\\n            \"pageSize\": 1,\\n\\n            \"offset\": 0,\\n\\n            \"unpaged\": false,\\n\\n            \"paged\": true\\n\\n        },\\n\\n        \"totalPages\": 3,\\n\\n        \"totalElements\": 3,\\n\\n        \"last\": false,\\n\\n        \"first\": true,\\n\\n        \"sort\": {\\n\\n            \"sorted\": true,\\n\\n            \"unsorted\": false,\\n\\n            \"empty\": false\\n\\n        },\\n\\n        \"numberOfElements\": 1,\\n\\n        \"size\": 1,\\n\\n        \"number\": 0,\\n\\n        \"empty\": false\\n\\n    },\\n\\n    \"state\": 1,\\n\\n    \"code\": \"10000\",\\n\\n    \"msg\": \"成功\"\\n\\n}\\n\\n返回\\n\\n失败\\n\\n示例\\n\\n{\\n\\n    \"data\": {\\n\\n        \"status\": 400,\\n\\n        \"timestamp\": \"2024-04-10 15:50:55\",\\n\\n        \"message\": \"taskCode:编码不能为空\"\\n\\n    },\\n\\n    \"state\": 0,\\n\\n    \"code\": \"10002\",\\n\\n    \"msg\": \"失败\"\\n\\n}\\n\\n示例\\n\\n备注\\n\\n参数名\\n\\n类型\\n\\n参数含义\\n\\n\\n\\ntaskCode\\n\\nstring\\n\\n任务规则编号\\n\\n\\n\\ntaskName\\n\\nstring\\n\\n任务规则名称\\n\\n\\n\\nexecutionTime\\n\\ndate\\n\\n执行时间\\n\\n\\n\\nissueFields\\n\\narray\\n\\n问题字段统计\\n\\n\\n\\nissueFields.zhName\\n\\nstring\\n\\n问题字段名称\\n\\n\\n\\nissueFields.type\\n\\nstring\\n\\n问题类型\\n\\n\\n\\nissueFields.preconditionIssue\\n\\nstring\\n\\n前置条件问题类型\\n\\n\\n\\nissueFields.amount\\n\\nint\\n\\n问题字段数量\\n\\n\\n\\ntotal\\n\\nint\\n\\n校验总量\\n\\n\\n\\nsucessTotal\\n\\nint\\n\\n正确数据量\\n\\n\\n\\nissueTotal\\n\\nint\\n\\n错误数据量\\n\\n备注\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n1.2校验结果详情接口\\n\\nURI\\n\\nGET /validation/api/external/results/detail\\n\\n功能\\n\\n获取校验接口详情\\n\\n\\n\\n参数名\\n\\n参数含义\\n\\n类型\\n\\n必选\\n\\nURL参数\\n\\n\\ttaskCode\\n\\n\\t校验任务编号\\n\\n\\tstring\\n\\n\\t是\\n\\n请求示例\\n\\nhttp://192.168.2.26:8051/validation/api/external/results/detail?taskCode=BSZNJY000055-0004\\n\\n返回\\n\\n结果\\n\\n参数\\n\\n参数名\\n\\n参数含义\\n\\n\\n\\ncode\\n\\n调用结果：\\n\\n10000-成功\\n\\n10002-失败\\n\\n\\n\\nmsg\\n\\n查询结果描述\\n\\n\\n\\ndata\\n\\n查询结果\\n\\n\\n\\nstate\\n\\n响应状态，0-失败，1-成功\\n\\n返回\\n\\n成功\\n\\n示例\\n\\n{\\n\\n    \"data\": {\\n\\n        \"taskName\": \"基本编码校验\",\\n\\n        \"total\": 5,\\n\\n        \"successTotal\": 0,\\n\\n        \"issueTotal\": 5,\\n\\n        \"issueFields\": [\\n\\n            {\\n\\n                \"enName\": \"zwfwCatalogCode\",\\n\\n                \"zhName\": \"基本编码\",\\n\\n                \"type\": \"测试字典类型11\",\\n\\n                \"preconditionIssue\": null,\\n\\n                \"amount\": \"5\"\\n\\n            }\\n\\n        ],\\n\\n        \"head\": [\\n\\n            \"基本编码\",\\n\\n            \"证件类型\"\\n\\n        ],\\n\\n        \"validationResultDetails\": [\\n\\n            {\\n\\n                \"row\": [\\n\\n                    \"2023-09-08\",\\n\\n                    \"2023-09-08\"\\n\\n                ],\\n\\n                \"zhName\": \"基本编码\",\\n\\n                \"value\": \"2023-09-08\",\\n\\n                \"type\": \"测试字典类型11\",\\n\\n                \"preconditionIssue\": null\\n\\n            },\\n\\n            {\\n\\n                \"row\": [\\n\\n                    \"2023-09-08\",\\n\\n                    \"2023-09-09\"\\n\\n                ],\\n\\n                \"zhName\": \"基本编码\",\\n\\n                \"value\": \"2023-09-08\",\\n\\n                \"type\": \"测试字典类型11\",\\n\\n                \"preconditionIssue\": null\\n\\n            },\\n\\n            {\\n\\n                \"row\": [\\n\\n                    \"2023-09-08\",\\n\\n                    \"2023-09-07\"\\n\\n                ],\\n\\n                \"zhName\": \"基本编码\",\\n\\n                \"value\": \"2023-09-08\",\\n\\n                \"type\": \"测试字典类型11\",\\n\\n                \"preconditionIssue\": null\\n\\n            },\\n\\n            {\\n\\n                \"row\": [\\n\\n                    \"\",\\n\\n                    \"2023-09-08\"\\n\\n                ],\\n\\n                \"zhName\": \"基本编码\",\\n\\n                \"value\": \"\",\\n\\n                \"type\": \"测试字典类型11\",\\n\\n                \"preconditionIssue\": null\\n\\n            },\\n\\n            {\\n\\n                \"row\": [\\n\\n                    \"2023-09-08\",\\n\\n                    \"\"\\n\\n                ],\\n\\n                \"zhName\": \"基本编码\",\\n\\n                \"value\": \"2023-09-08\",\\n\\n                \"type\": \"测试字典类型11\",\\n\\n                \"preconditionIssue\": null\\n\\n            }\\n\\n        ]\\n\\n    },\\n\\n    \"state\": 1,\\n\\n    \"code\": \"10000\",\\n\\n    \"msg\": \"成功\"\\n\\n}\\n\\n返回\\n\\n失败\\n\\n示例\\n\\n{\\n\\n    \"data\": {\\n\\n        \"status\": 400,\\n\\n        \"timestamp\": \"2024-04-10 16:03:00\",\\n\\n        \"message\": \"detail.taskCode: 校验任务编号不能为空\"\\n\\n    },\\n\\n    \"state\": 0,\\n\\n    \"code\": \"10002\",\\n\\n    \"msg\": \"失败\"\\n\\n}\\n\\n示例\\n\\n备注\\n\\n参数名\\n\\n类型\\n\\n参数含义\\n\\n\\n\\ntaskName\\n\\nstring\\n\\n任务规则名称\\n\\n\\n\\ntaskCode\\n\\nstring\\n\\n任务编号\\n\\n\\n\\nissueFields\\n\\narray\\n\\n问题字段统计\\n\\n\\n\\nissueFields.zhName\\n\\nstring\\n\\n问题字段名称\\n\\n\\n\\nissueFields.type\\n\\nstring\\n\\n问题类型\\n\\n\\n\\nissueFields.preconditionIssue\\n\\nstring\\n\\n前置条件问题类型\\n\\n\\n\\nissueFields.amount\\n\\nint\\n\\n问题字段数量\\n\\n\\n\\ntotal\\n\\nint\\n\\n校验总量\\n\\n\\n\\nsucessTotal\\n\\nint\\n\\n正确数据量\\n\\n\\n\\nissueTotal\\n\\nint\\n\\n错误数据量\\n\\n\\n\\nvalidationResultDetails\\n\\narray\\n\\n校验结果详情\\n\\n\\n\\nvalidationResultDetails.row\\n\\narray\\n\\n校验的数据行\\n\\n\\n\\nvalidationResultDetails.zhName\\n\\nstring\\n\\n问题字段名\\n\\n\\n\\nvalidationResultDetails.value\\n\\nstring\\n\\n字段值\\n\\n\\n\\nvalidationResultDetails.type\\n\\nstring\\n\\n问题类型\\n\\n\\n\\nvalidationResultDetails.preconditionIssue\\n\\nstring\\n\\n前置条件\\n\\n\\n\\nhead\\n\\narray\\n\\n校验数据表头\\n\\n备注\\n\\n\\n\\n\\n\\n\\n\\t规则能力接口\\n\\n2.1规则校验接口\\n\\nURI\\n\\nPOST /validation/api/external/rules/execute\\n\\n功能\\n\\n规则能力调用\\n\\n\\n\\n参数名\\n\\n参数含义\\n\\n类型\\n\\n必选\\n\\n请求体参数\\n\\n\\truleCode\\n\\n\\t规则编码\\n\\n\\tstring\\n\\n\\t是\\n\\n\\n\\n\\tvalue\\n\\n\\t被校验的值(默认值为空字符串)\\n\\n\\tstring\\n\\n\\t否\\n\\n\\n\\n\\tfield\\n\\n\\t字段名称\\n\\n\\tString\\n\\n\\t否\\n\\n请求示例\\n\\n[{\\n\\n\\t\\t\"ruleCode\": \"GZ0000511\",\\n\\n\\t\\t\"value\": \"100\",\\n\\n\\t\\t\"field\": \"年龄\"\\n\\n\\t},\\n\\n\\t{\\n\\n\\t\\t\"ruleCode\": \"GZ0000512\",\\n\\n\\t\\t\"value\": \"张三\",\\n\\n\\t\\t\"field\": \"姓名\"\\n\\n\\t}\\n\\n]\\n\\n返回\\n\\n结果\\n\\n参数\\n\\n参数名\\n\\n参数含义\\n\\n\\n\\ncode\\n\\n调用结果：\\n\\n10000-成功\\n\\n10002-失败\\n\\n\\n\\nmsg\\n\\n查询结果描述\\n\\n\\n\\ndata\\n\\n查询结果\\n\\n\\n\\nstate\\n\\n响应状态，0-失败，1-成功\\n\\n返回\\n\\n成功\\n\\n示例\\n\\n\\n\\n{\\n\\n    \"data\": [{\\n\\n\\t\\t\"ruleCode\": \"GZ0000511\",\\n\\n\\t\\t\"value\": \"100\",\\n\\n\\t\\t\"field\": \"年龄\",\\n\\n\\t\\t\"result\": 0\\n\\n\\t},\\n\\n\\t{\\n\\n\\t\\t\"ruleCode\": \"GZ0000512\",\\n\\n\\t\\t\"value\": \"张三\",\\n\\n\\t\\t\"field\": \"姓名\",\\n\\n\\t\\t\"result\": 1\\n\\n\\t}\\n\\n],\\n\\n    \"state\": 1,\\n\\n    \"code\": \"10000\",\\n\\n    \"msg\": \"成功\"\\n\\n}\\n\\n\\n\\n返回\\n\\n失败\\n\\n示例\\n\\n{\\n\\n    \"data\": {\\n\\n        \"status\": 400,\\n\\n        \"timestamp\": \"2024-04-24 11:47:51\",\\n\\n        \"message\": \"规则不存在\"\\n\\n    },\\n\\n    \"state\": 0,\\n\\n    \"code\": \"10002\",\\n\\n    \"msg\": \"失败\"\\n\\n}\\n\\n示例\\n\\n备注\\n\\n参数名\\n\\n类型\\n\\n参数含义\\n\\n\\n\\nresult\\n\\nstring\\n\\n执行结果 0通过、1不通过\\n\\n备注')]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_community.document_loaders import Docx2txtLoader\n", "\n", "# 定义chatdoc\n", "class ChatDoc():\n", "    def getFile():\n", "        # 读取文件\n", "        loader = Docx2txtLoader(\"test.docx\")\n", "        docs = loader.load()\n", "        return docs\n", "ChatDoc.getFile()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting pandas\n", "  Downloading pandas-2.3.0-cp313-cp313-macosx_11_0_arm64.whl.metadata (91 kB)\n", "Collecting openpyxl\n", "  Downloading openpyxl-3.1.5-py2.py3-none-any.whl.metadata (2.5 kB)\n", "Requirement already satisfied: numpy>=1.26.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from pandas) (2.2.4)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from pandas) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from pandas) (2025.2)\n", "Collecting tzdata>=2022.7 (from pandas)\n", "  Downloading tzdata-2025.2-py2.py3-none-any.whl.metadata (1.4 kB)\n", "Collecting et-xmlfile (from openpyxl)\n", "  Downloading et_xmlfile-2.0.0-py3-none-any.whl.metadata (2.7 kB)\n", "Requirement already satisfied: six>=1.5 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from python-dateutil>=2.8.2->pandas) (1.17.0)\n", "Downloading pandas-2.3.0-cp313-cp313-macosx_11_0_arm64.whl (10.7 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m10.7/10.7 MB\u001b[0m \u001b[31m34.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hDownloading openpyxl-3.1.5-py2.py3-none-any.whl (250 kB)\n", "Downloading tzdata-2025.2-py2.py3-none-any.whl (347 kB)\n", "Downloading et_xmlfile-2.0.0-py3-none-any.whl (18 kB)\n", "Installing collected packages: tzdata, et-xmlfile, pandas, openpyxl\n", "Successfully installed et-xmlfile-2.0.0 openpyxl-3.1.5 pandas-2.3.0 tzdata-2025.2\n"]}], "source": ["! pip install pandas openpyxl"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'source': 'test.xlsx'}, page_content='               发起时间                完成时间    发起人姓名   发起人部门 请假类型          开始时间          结束时间   时长\\n2024-12-31 11:16:45 2024-12-31 12:07:56      王小兮    研发三部   年假 2025-01-02 上午 2025-01-03 下午   2天\\n2024-12-31 11:09:07 2024-12-31 11:23:46      许士粟    研发三部   调休 2025-01-02 上午 2025-01-03 下午   2天\\n2024-12-31 11:02:34 2024-12-31 17:24:34      王茂全   智办运营部   调休 2025-01-06 上午 2025-01-06 下午   1天\\n2024-12-31 10:29:03 2024-12-31 10:29:50      付东明    研发一部   年假 2024-12-23 上午 2024-12-25 下午   3天\\n2024-12-31 10:27:35 2024-12-31 10:29:38      付东明    研发一部   年假 2024-12-30 上午 2024-12-31 下午   2天\\n2024-12-31 08:53:50 2024-12-31 15:30:18       兰慧   交付实施部   调休 2024-12-31 上午 2024-12-31 下午   1天\\n2024-12-30 23:07:37 2025-01-02 14:18:13       陈星   市场营销部   年假 2024-12-31 上午 2025-01-03 下午   3天\\n2024-12-30 22:33:21 2024-12-30 23:14:08       吴迪   智办运营部   调休 2025-01-02 上午 2025-01-03 下午   2天\\n2024-12-30 21:18:40 2024-12-31 18:52:03      黄传英   智办运营部   年假 2024-12-31 上午 2024-12-31 下午   1天\\n2024-12-30 20:40:26 2025-01-02 09:20:38      冯德军   质量监查部   年假 2024-12-31 上午 2024-12-31 下午   1天\\n2024-12-30 18:31:33 2024-12-30 19:10:49      裴琳琳    研发三部   调休 2025-01-02 上午 2025-01-03 下午   2天\\n2024-12-30 18:16:30 2024-12-30 18:41:43      张美婷 智能助理运营部   调休 2024-12-31 下午 2024-12-31 下午 0.5天\\n2024-12-30 18:15:34 2024-12-30 19:19:34      李丹辉    研发一部   调休 2025-01-02 上午 2025-01-03 下午   2天\\n2024-12-30 17:42:19 2024-12-30 19:19:18       刘潭    研发一部   年假 2024-12-31 上午 2024-12-31 下午   1天\\n2024-12-30 16:25:40 2024-12-30 17:08:10       殷鹏    研发三部   调休 2025-01-03 下午 2025-01-03 下午 0.5天\\n2024-12-30 16:24:53 2024-12-30 17:08:09       殷鹏    研发三部   年假 2025-01-02 上午 2025-01-03 上午 1.5天\\n2024-12-30 16:21:48 2024-12-30 19:19:51      蔡荣诠    研发一部   调休 2024-12-31 上午 2024-12-31 下午   1天\\n2024-12-30 16:13:28 2024-12-30 16:33:37      方程伟    研发一部   调休 2025-01-02 上午 2025-01-03 下午   2天\\n2024-12-30 15:39:55 2024-12-30 15:49:12      魏爱红   产品创新部   年假 2024-12-31 上午 2024-12-31 下午   1天\\n2024-12-30 15:16:56 2024-12-30 16:07:27      魏向嵘   质量监查部   年假 2024-12-31 上午 2024-12-31 下午   1天\\n2024-12-30 15:15:33 2024-12-30 16:14:39      杨昕妍   产品创新部   调休 2024-12-31 上午 2024-12-31 下午   1天\\n2024-12-30 15:00:48 2024-12-30 15:14:20      胡爱兵   交付实施部   调休 2024-12-30 下午 2024-12-30 下午 0.5天\\n2024-12-30 14:43:13 2024-12-30 14:54:31      江国平    研发二部   调休 2025-01-02 上午 2025-01-03 下午   2天\\n2024-12-30 14:41:12 2024-12-30 14:54:46      刘忠赛   质量监查部   调休 2025-01-02 上午 2025-01-03 下午   2天\\n2024-12-30 14:34:58 2025-01-02 12:52:12       韩露   交付实施部   调休 2025-01-20 上午 2025-01-24 下午   5天\\n2024-12-30 14:27:07 2024-12-30 22:14:19      李登伟   交付实施部   年假 2025-01-20 上午 2025-01-27 下午   7天\\n2024-12-30 13:48:52 2024-12-30 15:08:38      邵珊珊   交付实施部   调休 2025-01-06 下午 2025-01-06 下午 0.5天\\n2024-12-30 13:18:23 2024-12-30 19:58:55       李帅   交付实施部   调休 2024-12-31 上午 2024-12-31 下午   1天\\n2024-12-30 12:22:24 2024-12-30 22:12:12      李云龙   交付实施部   调休 2025-01-20 上午 2025-01-24 下午   5天\\n2024-12-30 11:20:47 2024-12-30 14:20:01       吴翔   交付实施部   调休 2025-01-02 上午 2025-01-03 下午   2天\\n2024-12-30 10:38:09 2024-12-30 11:43:50       唐欢   质量监查部   调休 2024-12-31 上午 2024-12-31 下午   1天\\n2024-12-30 10:21:50 2024-12-30 11:35:06      刘春辉   交付实施部   调休 2024-12-31 下午 2024-12-31 下午 0.5天\\n2024-12-30 10:16:22 2024-12-30 12:18:01      贾正全    研发二部   调休 2024-12-31 下午 2024-12-31 下午 0.5天\\n2024-12-30 10:00:30 2024-12-30 10:26:59      郭鹏军   质量监查部   年假 2024-12-31 上午 2024-12-31 下午   1天\\n2024-12-30 09:46:00 2024-12-30 09:50:58       马龙    研发三部   年假 2024-12-30 上午 2024-12-30 上午 0.5天\\n2024-12-30 09:38:37 2024-12-31 14:40:37      金翠佩 智能助理运营部   年假 2025-01-02 上午 2025-01-03 下午   2天\\n2024-12-30 09:30:42 2024-12-30 14:20:00       夏越   交付实施部   调休 2025-01-02 上午 2025-01-03 上午 1.5天\\n2024-12-30 09:27:32 2024-12-30 10:07:46       王维   交付实施部   调休 2024-12-31 上午 2025-01-02 下午   2天\\n2024-12-30 09:18:06 2024-12-30 09:57:28      姚家星    研发三部   年假 2024-12-30 上午 2024-12-30 上午 0.5天\\n2024-12-30 08:08:44 2024-12-31 14:40:41      潘露晨 智能助理运营部   调休 2024-12-30 上午 2024-12-30 下午   1天\\n2024-12-30 00:58:18 2024-12-30 10:07:50       陆遥   交付实施部   调休 2024-12-30 上午 2024-12-31 下午   2天\\n2024-12-29 23:32:37 2024-12-30 08:08:08      张藻龙   产品创新部   年假 2025-01-02 上午 2025-01-03 下午   2天\\n2024-12-29 23:13:24 2024-12-30 08:08:14       郭建   产品创新部   调休 2024-12-30 上午 2024-12-30 上午 0.5天\\n2024-12-29 21:44:52 2024-12-30 09:17:31      金晓芳    研发一部   年假 2024-12-30 上午 2024-12-30 下午   1天\\n2024-12-29 21:15:48 2024-12-29 23:04:15       冯闯    研发三部   调休 2024-12-30 上午 2024-12-31 下午   2天\\n2024-12-29 20:50:46 2024-12-29 21:34:55       刘峰   交付实施部   调休 2024-12-30 上午 2024-12-31 下午   2天\\n2024-12-29 19:26:55 2024-12-29 19:34:36       韩凯   产品创新部   调休 2024-12-30 上午 2024-12-30 下午   1天\\n2024-12-29 18:13:40 2024-12-29 19:57:33      张艳帅    研发三部   调休 2024-12-30 上午 2024-12-31 下午   2天\\n2024-12-29 17:10:31 2024-12-29 17:19:38      宋志刚    研发三部   调休 2024-12-30 上午 2024-12-31 下午   2天\\n2024-12-29 11:31:19 2024-12-30 18:42:06       马恋 智能助理运营部   调休 2024-12-31 上午 2024-12-31 下午   1天\\n2024-12-29 10:48:04 2024-12-30 10:22:02      侯启仁   交付实施部   调休 2024-12-30 上午 2024-12-31 下午   2天\\n2024-12-29 08:29:13 2024-12-29 23:32:42      徐宏方   交付实施部   调休 2024-12-30 下午 2024-12-31 下午 1.5天\\n2024-12-28 08:26:49 2024-12-28 21:48:57      王志文    研发三部   年假 2024-12-30 上午 2024-12-31 下午   2天\\n2024-12-27 21:34:09 2024-12-27 23:07:23 瞿佳辉(已离职) 智能助理运营部   调休 2024-12-30 上午 2024-12-31 下午   2天\\n2024-12-27 18:26:55 2024-12-27 22:36:27  李旭(已离职)   智办运营部   调休 2025-01-03 上午 2025-01-03 下午   1天\\n2024-12-27 18:25:50 2024-12-27 22:36:25  李旭(已离职)   智办运营部   年假 2025-01-02 上午 2025-01-02 下午   1天\\n2024-12-27 18:25:01 2024-12-27 22:36:21  李旭(已离职)   智办运营部   调休 2024-12-30 上午 2024-12-31 下午   2天\\n2024-12-27 18:21:55 2024-12-27 22:36:30  李旭(已离职)   智办运营部   调休 2025-01-06 上午 2025-01-10 下午   5天\\n2024-12-27 17:34:32 2024-12-27 20:12:53      王晶晶    研发一部   年假 2024-12-30 上午 2024-12-31 下午   2天\\n2024-12-27 16:57:21 2024-12-28 18:42:14      唐恬馨 智能助理运营部   年假 2024-12-30 上午 2024-12-31 下午   2天\\n2024-12-27 15:33:08 2024-12-27 18:05:49      郭培林    研发三部   调休 2024-12-30 上午 2025-01-03 下午   4天\\n2024-12-27 14:55:41 2024-12-27 15:29:26      林云龙    研发三部   调休 2024-09-27 下午 2024-09-27 下午 0.5天\\n2024-12-27 13:56:36 2024-12-27 15:26:38      马瑞阳 智能助理运营部   调休 2024-12-30 上午 2024-12-31 下午   2天\\n2024-12-27 13:52:37 2024-12-27 14:48:15       常伟    研发三部   年假 2024-12-30 上午 2024-12-31 下午   2天\\n2024-12-27 13:35:46 2024-12-27 14:03:36      范建伟    研发三部   调休 2024-12-27 下午 2024-12-27 下午 0.5天\\n2024-12-27 12:32:57 2024-12-27 14:20:32       丁昊   质量监查部   年假 2024-12-30 上午 2024-12-31 下午   2天\\n2024-12-27 11:21:51 2024-12-27 14:23:17       何刚    研发一部   调休 2024-12-30 上午 2024-12-31 下午   2天\\n2024-12-27 11:05:57 2024-12-27 14:20:36      黄腾龙   质量监查部   年假 2024-12-27 下午 2024-12-27 下午 0.5天\\n2024-12-27 10:56:43 2024-12-27 12:23:07      陆明星    研发一部   年假 2024-12-30 上午 2024-12-30 上午 0.5天\\n2024-12-27 10:24:38 2024-12-27 12:23:19      陈英华    研发一部   年假 2024-12-30 上午 2024-12-31 下午   2天\\n2024-12-27 10:23:15 2024-12-27 18:05:27      沈忻荣   智办运营部   年假 2024-12-31 下午 2024-12-31 下午 0.5天\\n2024-12-27 10:11:03 2024-12-27 14:23:29       成莉    研发一部   年假 2024-12-30 上午 2024-12-30 下午   1天\\n2024-12-27 09:46:33 2024-12-27 12:39:59       张征    研发三部   调休 2024-12-31 上午 2025-01-03 下午   3天\\n2024-12-27 09:34:42 2024-12-27 11:33:59       余晨    研发三部   调休 2024-12-30 上午 2024-12-30 下午   1天\\n2024-12-27 08:19:57 2024-12-27 12:29:17       林龙    研发一部   调休 2024-12-27 上午 2024-12-27 下午   1天\\n2024-12-27 07:32:50 2024-12-27 11:20:08      刘俊勇    研发三部   年假 2024-12-27 上午 2024-12-27 下午   1天\\n2024-12-27 00:20:36 2024-12-27 12:09:03    王斌-运维   交付实施部   调休 2025-01-02 上午 2025-01-03 下午   2天\\n2024-12-26 23:11:11 2024-12-27 00:15:42      叶林峰   交付实施部   调休 2024-12-27 下午 2024-12-27 下午 0.5天\\n2024-12-26 21:10:47 2024-12-26 21:25:35       张淼   交付实施部   年假 2024-12-30 上午 2024-12-31 下午   2天\\n2024-12-26 20:45:24 2024-12-26 21:17:20      宋志刚    研发三部   调休 2024-12-27 上午 2024-12-27 上午 0.5天\\n2024-12-26 20:20:53 2024-12-27 15:18:47       谭剑    研发二部   年假 2024-12-26 上午 2024-12-27 下午   2天\\n2024-12-26 19:29:50 2024-12-27 13:40:16      黄国庆 智能助理运营部   调休 2024-12-27 上午 2024-12-27 下午   1天\\n2024-12-26 17:53:30 2024-12-27 14:28:23       付泽    研发一部   调休 2024-12-31 上午 2024-12-31 下午   1天\\n2024-12-26 17:52:45 2024-12-27 14:28:16       付泽    研发一部   年假 2024-12-30 上午 2024-12-30 下午   1天\\n2024-12-26 17:40:35 2024-12-26 18:33:15       王军   质量监查部   调休 2024-12-30 上午 2024-12-31 下午   2天\\n2024-12-26 17:33:50 2024-12-27 15:44:33       滕虎   交付实施部   调休 2024-12-30 上午 2025-01-03 下午   4天\\n2024-12-26 17:31:12 2024-12-26 17:31:22      江丽媛   市场营销部   事假 2024-12-26 下午 2024-12-26 下午 0.5天\\n2024-12-26 14:39:00 2024-12-26 14:57:54      柳汉芳   产品创新部   年假 2025-01-02 上午 2025-01-03 下午   2天\\n2024-12-26 14:31:22 2024-12-27 15:42:41      王永峰   咨询服务部   调休 2024-12-27 上午 2025-01-03 下午   5天\\n2024-12-26 11:50:18 2024-12-26 13:07:06      张嘉莉   智办运营部   年假 2024-12-27 上午 2024-12-27 下午   1天\\n2024-12-26 11:25:11 2024-12-27 16:49:41      李圭显   质量监查部   调休 2024-12-30 上午 2024-12-31 下午   2天\\n2024-12-26 11:11:38 2024-12-26 14:37:21      张江涛    研发三部   调休 2024-12-27 上午 2024-12-27 下午   1天\\n2024-12-26 10:53:50 2024-12-26 13:07:08      殷钟麟   智办运营部   年假 2024-12-27 上午 2024-12-27 下午   1天\\n2024-12-26 10:32:07 2024-12-27 15:08:50      姜万伟    研发二部   调休 2024-12-26 下午 2024-12-27 下午 1.5天\\n2024-12-26 09:56:25 2024-12-26 11:30:46      雷学田    研发三部   调休 2024-12-27 上午 2024-12-27 下午   1天\\n2024-12-26 09:48:35 2024-12-27 13:11:36      曾雪娇   综合服务部  产检假    2024-12-27    2024-12-27   1天\\n2024-12-26 08:45:48 2024-12-26 09:05:59       黄婷   交付实施部   年假 2024-12-26 上午 2024-12-27 下午   2天\\n2024-12-26 08:37:24 2024-12-27 15:08:54      江国平    研发二部   调休 2024-12-26 上午 2024-12-26 上午 0.5天\\n2024-12-26 08:28:02 2024-12-26 21:51:44       陈旭    研发二部   调休 2024-12-26 上午 2024-12-26 下午   1天\\n2024-12-25 22:52:35 2024-12-25 22:55:28      蒋剑锋   交付实施部   年假 2024-12-27 上午 2024-12-27 下午   1天\\n2024-12-25 21:31:59 2024-12-26 16:11:11       詹云   交付实施部   调休 2025-01-02 上午 2025-01-03 下午   2天\\n2024-12-25 21:31:06 2024-12-26 16:11:14       詹云   交付实施部   调休 2024-12-30 上午 2024-12-31 下午   2天\\n2024-12-25 21:14:32 2024-12-26 10:58:07      潘多润   交付实施部   年假 2024-12-26 上午 2024-12-26 下午   1天\\n2024-12-25 20:41:19 2024-12-25 21:08:48       程强   交付实施部   调休 2024-12-30 上午 2024-12-31 下午   2天\\n2024-12-25 19:21:44 2024-12-26 00:46:33       易琼   智办运营部   调休 2025-01-02 上午 2025-01-03 下午   2天\\n2024-12-25 18:48:18 2024-12-26 07:34:18      吴家豪   产品创新部   年假 2024-12-31 上午 2025-01-03 下午   3天\\n2024-12-25 18:08:33 2024-12-25 20:19:34       赵秦    研发二部   调休 2024-12-27 上午 2024-12-27 下午   1天\\n2024-12-25 17:42:35 2024-12-25 18:38:45       周建    研发三部   调休 2024-12-26 上午 2024-12-26 下午   1天\\n2024-12-25 16:25:04 2024-12-25 18:12:36      任嘉瑞    研发二部   调休 2024-12-27 AM 2024-12-27 PM   1天\\n2024-12-25 16:21:52 2024-12-26 13:52:32      陆盛尧   咨询服务部   年假 2025-01-26 上午 2025-01-27 下午   2天\\n2024-12-25 15:28:10 2024-12-25 21:10:16      秦登全   交付实施部   调休 2024-12-30 上午 2024-12-31 下午   2天\\n2024-12-25 14:16:24 2024-12-25 15:18:07       周超    研发一部   调休 2024-12-25 上午 2024-12-25 上午 0.5天\\n2024-12-25 12:36:42 2024-12-26 16:03:33       马骁   智办运营部   调休 2024-12-25 下午 2024-12-26 下午 1.5天\\n2024-12-25 11:40:32 2024-12-25 13:20:24      董琳娜   产品创新部   事假 2025-01-02 上午 2025-01-03 下午   2天\\n2024-12-25 11:26:11 2024-12-25 11:34:35       王鹏   交付实施部   调休 2024-12-25 下午 2024-12-25 下午 0.5天\\n2024-12-25 11:22:40 2024-12-25 13:34:29      管小珠   质量监查部   调休 2025-01-23 上午 2025-01-24 下午   2天\\n2024-12-25 11:19:25 2024-12-25 11:19:36      管小珠   质量监查部   调休 2025-01-20 上午 2025-01-22 下午   3天\\n2024-12-25 11:12:57 2024-12-25 22:53:43       陈转   交付实施部   病假 2024-12-26 上午 2024-12-31 下午   4天\\n2024-12-25 11:06:41 2024-12-25 11:19:32       冯宇   质量监查部   年假 2024-12-30 上午 2024-12-31 下午   2天\\n2024-12-25 10:14:43 2024-12-26 13:07:12      张娜娜 智能助理运营部   调休 2024-12-26 下午 2024-12-26 下午 0.5天\\n2024-12-25 09:59:58 2024-12-25 10:05:13       彭爽   智办运营部   调休 2024-12-30 上午 2024-12-31 下午   2天\\n2024-12-25 08:00:05 2024-12-26 13:52:38      喻学俊   咨询服务部   调休 2024-12-25 上午 2024-12-25 上午 0.5天\\n2024-12-25 07:58:51 2024-12-25 08:20:39       陈转   交付实施部   病假 2024-12-25 上午 2024-12-25 下午   1天\\n2024-12-24 21:40:24 2024-12-25 10:10:45       孙艺    研发三部   年假 2024-12-25 上午 2024-12-25 下午   1天\\n2024-12-24 18:11:34 2024-12-24 18:31:25       孙杰    研发一部   调休 2024-12-25 上午 2024-12-25 上午 0.5天\\n2024-12-24 17:47:25 2024-12-25 15:59:24      郭健辉    研发一部   调休 2024-12-25 上午 2024-12-27 下午   3天\\n2024-12-24 17:27:42 2024-12-24 17:32:01      李如意   智办运营部   调休 2024-12-25 上午 2024-12-25 下午   1天\\n2024-12-24 16:07:37 2024-12-24 16:26:58      杜振杰   智办运营部   调休 2024-12-30 上午 2024-12-31 下午   2天\\n2024-12-24 15:42:17 2024-12-25 16:41:55      俞斐律   智办运营部   年假 2024-12-27 上午 2024-12-31 下午   3天\\n2024-12-24 15:36:29 2024-12-24 16:29:01      方程伟    研发一部   年假 2024-12-26 上午 2024-12-26 下午   1天\\n2024-12-24 15:30:21 2024-12-24 15:30:32       洪磊   智办运营部   年假 2024-12-25 下午 2024-12-25 下午 0.5天\\n2024-12-24 15:29:38 2024-12-24 15:34:37      孔德秧   智办运营部   年假 2025-01-26 上午 2025-01-27 下午   2天\\n2024-12-24 14:26:17 2024-12-24 14:47:29       高赫   交付实施部   调休 2024-12-25 上午 2024-12-26 下午   2天\\n2024-12-24 14:06:58 2024-12-24 14:10:08      刘海宁   质量监查部   调休 2025-01-17 上午 2025-01-17 下午   1天\\n2024-12-24 13:38:59 2024-12-24 14:37:57       肖凡    研发三部   调休 2024-12-30 上午 2025-01-03 下午   4天\\n2024-12-24 12:07:21 2024-12-24 13:58:08       肖璐   产品创新部   年假 2024-12-30 上午 2024-12-31 下午   2天\\n2024-12-24 11:55:56 2024-12-24 12:23:21       樊凯    研发一部   年假 2024-12-24 上午 2024-12-24 下午   1天\\n2024-12-24 11:51:05 2024-12-24 12:33:29      张建波    研发二部   调休 2024-12-24 下午 2024-12-24 下午 0.5天\\n2024-12-24 11:22:28 2024-12-24 13:36:27      曾慧敏   智办运营部   调休 2025-02-12 上午 2025-02-13 下午   2天\\n2024-12-24 11:22:10 2024-12-24 13:36:29      曾慧敏   智办运营部   调休 2025-02-10 上午 2025-02-11 下午   2天\\n2024-12-24 11:21:44 2024-12-24 13:36:30      曾慧敏   智办运营部   调休 2025-02-07 上午 2025-02-08 下午   2天\\n2024-12-24 11:21:18 2024-12-24 13:36:28      曾慧敏   智办运营部   调休 2025-02-05 上午 2025-02-06 下午   2天\\n2024-12-24 11:16:00 2024-12-26 10:04:51      沈安桐 智能助理运营部   年假 2024-12-31 下午 2024-12-31 下午 0.5天\\n2024-12-24 10:58:10 2024-12-24 13:29:28      杨晓昀   质量监查部   调休 2024-12-25 上午 2024-12-25 下午   1天\\n2024-12-24 10:57:22 2024-12-24 13:36:26      张铭政   智办运营部   调休 2025-01-26 上午 2025-01-27 下午   2天\\n2024-12-24 10:56:36 2024-12-24 13:36:25      张铭政   智办运营部   调休 2025-01-23 上午 2025-01-24 下午   2天\\n2024-12-24 10:55:15 2024-12-24 11:18:32      张铭政   智办运营部   年假 2025-01-20 上午 2025-01-22 下午   3天\\n2024-12-24 10:39:19 2024-12-24 10:57:46      吴胜强   质量监查部   调休 2024-12-25 上午 2024-12-25 下午   1天\\n2024-12-24 09:46:23 2024-12-24 10:17:12      钟旭南    研发三部   调休 2024-12-26 下午 2024-12-26 下午 0.5天\\n2024-12-24 09:09:05 2024-12-24 09:09:31      宋志刚    研发三部   调休 2024-12-25 上午 2024-12-25 下午   1天\\n2024-12-24 06:46:55 2024-12-24 09:05:03       冯振    研发三部   年假 2024-12-24 上午 2024-12-24 下午   1天\\n2024-12-23 22:05:23 2024-12-23 23:59:47      王宏亮    研发三部   调休 2024-12-25 上午 2024-12-25 下午   1天\\n2024-12-23 21:52:51 2024-12-24 12:23:47       刘贝    研发一部   年假 2024-12-25 上午 2024-12-25 下午   1天\\n2024-12-23 21:22:36 2024-12-24 10:28:06      孙凯宇 智能助理运营部   年假 2024-12-24 上午 2024-12-24 下午   1天\\n2024-12-23 19:50:38 2024-12-23 22:36:59       黄辉    研发三部   调休 2024-12-24 上午 2024-12-24 下午   1天\\n2024-12-23 19:10:53 2024-12-24 11:17:12       闫敏    研发一部   调休 2024-12-24 上午 2024-12-24 下午   1天\\n2024-12-23 18:36:07 2024-12-23 20:44:26      林云龙    研发三部   调休 2025-01-13 上午 2025-01-13 下午   1天\\n2024-12-23 18:35:26 2024-12-23 21:08:30      林云龙    研发三部   年假 2025-01-08 上午 2025-01-10 下午   3天\\n2024-12-23 17:43:21 2024-12-23 18:18:07      李晓习   产品创新部   调休 2024-12-24 上午 2024-12-24 下午   1天\\n2024-12-23 17:02:57 2024-12-23 17:37:51       袁通 智能助理运营部   事假 2024-12-24 上午 2024-12-24 下午   1天\\n2024-12-23 16:33:38 2024-12-23 16:40:01      郭培林    研发三部   调休 2024-12-24 上午 2024-12-25 下午   2天\\n2024-12-23 16:27:23 2024-12-23 20:48:30      徐宏方   交付实施部   年假 2024-12-24 上午 2024-12-25 上午 1.5天\\n2024-12-23 15:48:29 2024-12-23 16:01:00       蔡藏    研发三部   调休 2024-12-24 上午 2024-12-24 下午   1天\\n2024-12-23 15:34:08 2024-12-24 16:09:07       马双   咨询服务部   婚假    2025-01-15    2025-01-24  10天\\n2024-12-23 14:31:59 2024-12-23 15:04:58       丁昊   质量监查部   调休 2024-12-25 上午 2024-12-27 下午   3天\\n2024-12-23 13:40:08 2024-12-23 14:57:38       陈凯    研发一部   调休 2024-12-23 下午 2024-12-23 下午 0.5天\\n2024-12-23 13:39:47 2024-12-23 13:56:19      熊俊诚    研发三部   年假 2024-12-25 上午 2024-12-26 下午   2天\\n2024-12-23 13:00:48 2024-12-23 14:03:47      刘俊勇    研发三部   调休 2024-12-23 下午 2024-12-23 下午 0.5天\\n2024-12-23 11:51:15 2024-12-23 14:11:48       张祥   智办运营部   年假 2024-12-23 下午 2024-12-23 下午 0.5天\\n2024-12-23 11:37:58 2024-12-23 13:43:38       常伟    研发三部   年假 2024-12-24 上午 2024-12-27 下午   4天\\n2024-12-23 11:00:14 2024-12-23 11:25:20      张美婷 智能助理运营部   年假 2024-12-24 下午 2024-12-24 下午 0.5天\\n2024-12-23 10:54:44 2024-12-23 16:39:25      袁盼盼    研发一部   调休 2024-12-30 上午 2024-12-31 下午   2天\\n2024-12-23 10:13:35 2024-12-23 11:45:48       郭健   智办运营部   事假 2024-12-25 上午 2024-12-31 下午   5天\\n2024-12-23 10:11:33 2024-12-23 15:58:27      陈紫文    研发二部   年假 2024-12-26 下午 2024-12-26 下午 0.5天\\n2024-12-23 10:10:33 2024-12-23 15:58:14      陈紫文    研发二部   年假 2024-12-24 上午 2024-12-24 下午   1天\\n2024-12-23 09:51:08 2024-12-23 15:04:59       唐欢   质量监查部   调休 2024-12-18 上午 2024-12-18 下午   1天\\n2024-12-23 09:48:58 2024-12-23 11:25:23      孙凯宇 智能助理运营部   年假 2024-12-23 下午 2024-12-23 下午 0.5天\\n2024-12-23 09:29:00 2024-12-23 11:24:54       李雄   产品创新部   调休 2025-01-02 上午 2025-01-03 下午   2天\\n2024-12-23 09:19:58 2024-12-23 12:55:03      冯成龙    研发一部   年假 2024-12-23 上午 2024-12-23 下午   1天\\n2024-12-23 09:12:45 2024-12-23 11:03:17      范建伟    研发三部   调休 2024-12-23 上午 2024-12-23 上午 0.5天\\n2024-12-23 09:00:41 2024-12-23 11:05:49       彭劭   交付实施部   病假 2024-12-23 上午 2024-12-24 下午   2天\\n2024-12-23 00:05:13 2024-12-23 09:15:50       闫敏    研发一部   调休 2024-12-23 上午 2024-12-23 下午   1天')]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "from langchain.schema import Document\n", "\n", "# 定义chatdoc\n", "class ChatDoc():\n", "    def getFile():\n", "        # 使用pandas读取Excel文件\n", "        try:\n", "            df = pd.read_excel(\"test.xlsx\")\n", "            # 将DataFrame转换为字符串格式\n", "            content = df.to_string(index=False)\n", "            # 创建Document对象\n", "            docs = [Document(page_content=content, metadata={'source': 'test.xlsx'})]\n", "            return docs\n", "        except FileNotFoundError:\n", "            print(\"文件 test.xlsx 不存在，请确保文件在当前目录下\")\n", "            return []\n", "        except Exception as e:\n", "            print(f\"读取文件时出错: {e}\")\n", "            return []\n", "\n", "ChatDoc.getFile()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/2x/7ztnm7x5111gcvjp0x0nv6400000gn/T/ipykernel_78756/1020465171.py:4: LangChainDeprecationWarning: The class `ChatOpenAI` was deprecated in LangChain 0.0.10 and will be removed in 1.0. An updated version of the class exists in the :class:`~langchain-openai package and should be used instead. To use it run `pip install -U :class:`~langchain-openai` and import as `from :class:`~langchain_openai import ChatOpenAI``.\n", "  llm = ChatOpenAI(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["content='你好！😊 很高兴见到你～有什么我可以帮你的吗？' additional_kwargs={} response_metadata={'token_usage': {'completion_tokens': 15, 'prompt_tokens': 4, 'total_tokens': 19, 'completion_tokens_details': None, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 0}, 'prompt_cache_hit_tokens': 0, 'prompt_cache_miss_tokens': 4}, 'model_name': 'deepseek-chat', 'system_fingerprint': 'fp_8802369eaa_prod0425fp8', 'finish_reason': 'stop', 'logprobs': None} id='run-2b822274-b60b-4f59-91b0-d23b8377b6c4-0'\n"]}], "source": ["from langchain_community.chat_models import ChatOpenAI\n", "\n", "# 使用 ChatOpenAI 来连接 DeepSeek API\n", "llm = ChatOpenAI(\n", "    model=\"deepseek-chat\",\n", "    temperature=0,\n", "    openai_api_key=\"sk-778deaff83d44d3a864caa8f3cf46c75\",\n", "    openai_api_base=\"https://api.deepseek.com/v1\"\n", ")\n", "\n", "print(llm.invoke(\"你好\"))\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# 导入必须的包\n", "# Import the required packages\n", "from langchain_community.document_loaders import UnstructuredExcelLoader, Docx2txtLoader, PyPDFLoader\n", "from langchain_text_splitters import CharacterTextSplitter\n", "\n", "# 定义ChatDoc类\n", "# defining ChatDoc\n", "class ChatDoc():\n", "    def __init__(self):\n", "        self.doc = None\n", "        self.splitText = []  # 分割后的文本 splited text\n", "        self.file_path = None  # 存储文件路径\n", "    \n", "    def getFile(self, doc):\n", "        self.file_path = doc  # 存储文件路径\n", "        # 根据文件扩展名选择合适的加载器\n", "        loaders = {\n", "            \"docx\": Docx2txtLoader,\n", "            \"pdf\": <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "            \"xlsx\": UnstructuredExcelLoader,\n", "        }\n", "        file_extension = doc.split(\".\")[-1]\n", "        loader_class = loaders.get(file_extension)\n", "        if loader_class:\n", "            try:\n", "                loader = loader_class(doc)\n", "                text = loader.load()\n", "                return text\n", "            except Exception as e:\n", "                print(f\"Error loading {file_extension} files:{e}\")\n", "        else:\n", "            print(f\"Unsupported file extension: {file_extension}\")\n", "            return None\n", "\n", "    def splitSentence(self):\n", "        # 使用存储的文件路径\n", "        full_text = self.getFile(self.file_path)\n", "        if full_text != None:\n", "            # 对文档进行切割\n", "            text_splitter = CharacterTextSplitter(chunk_size=150, chunk_overlap=20)\n", "            texts = text_splitter.split_documents(full_text)\n", "            self.splitText = texts\n", "        else:\n", "            print(\"文件加载失败\")\n", "\n", "    def getSplitText(self):\n", "        return self.splitText"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Created a chunk of size 181, which is longer than the specified 150\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[Document(metadata={'source': 'test.docx'}, page_content='变更历史 \\n\\n版本号\\n\\n日期\\n\\n作者\\n\\n扼要说明\\n\\nV0.1.1\\n\\n2024-04-24\\n\\n胡欣\\n\\n新建文档，新增1.1、1.2、2.1接口描述\\n\\n1校验结果查询相关接口\\n\\n1.1校验结果列表接口\\n\\nURI\\n\\nGET /validation/api/external/results/list\\n\\n功能'), Document(metadata={'source': 'test.docx'}, page_content='功能\\n\\n获取校验结果列表\\n\\n参数名\\n\\n参数含义\\n\\n类型\\n\\n必选\\n\\nURL参数\\n\\n\\ttaskCode\\n\\n\\t校验规则编号\\n\\n\\tstring\\n\\n\\t是\\n\\n\\tstartExecTime\\n\\n\\t执行时间开始时间(yyyy-MM-dd HH:mm:ss)\\n\\n\\tdate\\n\\n\\t否\\n\\n\\tendExecTime'), Document(metadata={'source': 'test.docx'}, page_content='否\\n\\n\\tendExecTime\\n\\n\\t执行时间结束时间(yyyy-MM-dd HH:mm:ss)\\n\\n\\tdate\\n\\n\\t否\\n\\n\\tpreconditionIssueType\\n\\n\\t前置条件问题类型\\n\\n\\tstring\\n\\n\\t否\\n\\n\\tissueType\\n\\n\\t问题类型\\n\\n\\tstring\\n\\n\\t否\\n\\n\\tpage'), Document(metadata={'source': 'test.docx'}, page_content='string\\n\\n\\t否\\n\\n\\tpage\\n\\n\\t分页数（默认0）\\n\\n\\tint\\n\\n\\t否\\n\\n\\tsize\\n\\n\\t分页大小(默认10)\\n\\n\\tint\\n\\n\\t否\\n\\n请求示例'), Document(metadata={'source': 'test.docx'}, page_content='http://192.168.2.26:8051/validation/api/external/results/list?startExecTime=2024-03-11%2012:34:33&endExecTime=2024-03-13%2012:34:34&preconditionIssueType=GZBZQ&taskCode=BSZNJY000055'), Document(metadata={'source': 'test.docx'}, page_content='返回\\n\\n结果\\n\\n参数\\n\\n参数名\\n\\n参数含义\\n\\ncode\\n\\n调用结果：\\n\\n10000-成功\\n\\n10002-失败\\n\\nmsg\\n\\n查询结果描述\\n\\ndata\\n\\n查询结果\\n\\nstate\\n\\n响应状态，0-失败，1-成功\\n\\n返回\\n\\n成功\\n\\n示例\\n\\n{\\n\\n    \"data\": {'), Document(metadata={'source': 'test.docx'}, page_content='示例\\n\\n{\\n\\n    \"data\": {\\n\\n        \"content\": [\\n\\n            {\\n\\n                \"taskCode\": \"BSZNJY000055-0004\",\\n\\n                \"taskName\": \"测试222\",'), Document(metadata={'source': 'test.docx'}, page_content='\"executionTime\": \"2024-03-12 12:34:33\",\\n\\n                \"issueFields\": [\\n\\n                    {'), Document(metadata={'source': 'test.docx'}, page_content='\"enName\": null,\\n\\n                        \"zhName\": \"数学111\",\\n\\n                        \"type\": null,'), Document(metadata={'source': 'test.docx'}, page_content='\"preconditionIssue\": \"该值不准确\",\\n\\n                        \"amount\": \"1\"\\n\\n                    }\\n\\n                ],'), Document(metadata={'source': 'test.docx'}, page_content='],\\n\\n                \"total\": 1,\\n\\n                \"issueTotal\": 1,\\n\\n                \"successTotal\": 0\\n\\n            }\\n\\n        ],'), Document(metadata={'source': 'test.docx'}, page_content='],\\n\\n        \"pageable\": {\\n\\n            \"sort\": {\\n\\n                \"sorted\": true,\\n\\n                \"unsorted\": false,'), Document(metadata={'source': 'test.docx'}, page_content='\"empty\": false\\n\\n            },\\n\\n            \"pageNumber\": 0,\\n\\n            \"pageSize\": 1,\\n\\n            \"offset\": 0,'), Document(metadata={'source': 'test.docx'}, page_content='\"unpaged\": false,\\n\\n            \"paged\": true\\n\\n        },\\n\\n        \"totalPages\": 3,\\n\\n        \"totalElements\": 3,\\n\\n        \"last\": false,'), Document(metadata={'source': 'test.docx'}, page_content='\"first\": true,\\n\\n        \"sort\": {\\n\\n            \"sorted\": true,\\n\\n            \"unsorted\": false,\\n\\n            \"empty\": false\\n\\n        },'), Document(metadata={'source': 'test.docx'}, page_content='},\\n\\n        \"numberOfElements\": 1,\\n\\n        \"size\": 1,\\n\\n        \"number\": 0,\\n\\n        \"empty\": false\\n\\n    },\\n\\n    \"state\": 1,'), Document(metadata={'source': 'test.docx'}, page_content='\"state\": 1,\\n\\n    \"code\": \"10000\",\\n\\n    \"msg\": \"成功\"\\n\\n}\\n\\n返回\\n\\n失败\\n\\n示例\\n\\n{\\n\\n    \"data\": {\\n\\n        \"status\": 400,'), Document(metadata={'source': 'test.docx'}, page_content='\"timestamp\": \"2024-04-10 15:50:55\",\\n\\n        \"message\": \"taskCode:编码不能为空\"\\n\\n    },\\n\\n    \"state\": 0,\\n\\n    \"code\": \"10002\",\\n\\n    \"msg\": \"失败\"\\n\\n}'), Document(metadata={'source': 'test.docx'}, page_content='\"msg\": \"失败\"\\n\\n}\\n\\n示例\\n\\n备注\\n\\n参数名\\n\\n类型\\n\\n参数含义\\n\\ntaskCode\\n\\nstring\\n\\n任务规则编号\\n\\ntaskName\\n\\nstring\\n\\n任务规则名称\\n\\nexecutionTime\\n\\ndate\\n\\n执行时间\\n\\nissueFields\\n\\narray\\n\\n问题字段统计'), Document(metadata={'source': 'test.docx'}, page_content='array\\n\\n问题字段统计\\n\\nissueFields.zhName\\n\\nstring\\n\\n问题字段名称\\n\\nissueFields.type\\n\\nstring\\n\\n问题类型\\n\\nissueFields.preconditionIssue\\n\\nstring\\n\\n前置条件问题类型\\n\\nissueFields.amount'), Document(metadata={'source': 'test.docx'}, page_content='issueFields.amount\\n\\nint\\n\\n问题字段数量\\n\\ntotal\\n\\nint\\n\\n校验总量\\n\\nsucessTotal\\n\\nint\\n\\n正确数据量\\n\\nissueTotal\\n\\nint\\n\\n错误数据量\\n\\n备注\\n\\n1.2校验结果详情接口\\n\\nURI'), Document(metadata={'source': 'test.docx'}, page_content='备注\\n\\n1.2校验结果详情接口\\n\\nURI\\n\\nGET /validation/api/external/results/detail\\n\\n功能\\n\\n获取校验接口详情\\n\\n参数名\\n\\n参数含义\\n\\n类型\\n\\n必选\\n\\nURL参数\\n\\n\\ttaskCode\\n\\n\\t校验任务编号\\n\\n\\tstring\\n\\n\\t是\\n\\n请求示例'), Document(metadata={'source': 'test.docx'}, page_content='string\\n\\n\\t是\\n\\n请求示例\\n\\nhttp://192.168.2.26:8051/validation/api/external/results/detail?taskCode=BSZNJY000055-0004\\n\\n返回\\n\\n结果\\n\\n参数\\n\\n参数名\\n\\n参数含义\\n\\ncode\\n\\n调用结果：'), Document(metadata={'source': 'test.docx'}, page_content='参数含义\\n\\ncode\\n\\n调用结果：\\n\\n10000-成功\\n\\n10002-失败\\n\\nmsg\\n\\n查询结果描述\\n\\ndata\\n\\n查询结果\\n\\nstate\\n\\n响应状态，0-失败，1-成功\\n\\n返回\\n\\n成功\\n\\n示例\\n\\n{\\n\\n    \"data\": {\\n\\n        \"taskName\": \"基本编码校验\",'), Document(metadata={'source': 'test.docx'}, page_content='\"total\": 5,\\n\\n        \"successTotal\": 0,\\n\\n        \"issueTotal\": 5,\\n\\n        \"issueFields\": [\\n\\n            {'), Document(metadata={'source': 'test.docx'}, page_content='{\\n\\n                \"enName\": \"zwfwCatalogCode\",\\n\\n                \"zhName\": \"基本编码\",\\n\\n                \"type\": \"测试字典类型11\",'), Document(metadata={'source': 'test.docx'}, page_content='\"preconditionIssue\": null,\\n\\n                \"amount\": \"5\"\\n\\n            }\\n\\n        ],\\n\\n        \"head\": [\\n\\n            \"基本编码\",'), Document(metadata={'source': 'test.docx'}, page_content='\"基本编码\",\\n\\n            \"证件类型\"\\n\\n        ],\\n\\n        \"validationResultDetails\": [\\n\\n            {\\n\\n                \"row\": ['), Document(metadata={'source': 'test.docx'}, page_content='\"2023-09-08\",\\n\\n                    \"2023-09-08\"\\n\\n                ],\\n\\n                \"zhName\": \"基本编码\",'), Document(metadata={'source': 'test.docx'}, page_content='\"value\": \"2023-09-08\",\\n\\n                \"type\": \"测试字典类型11\",\\n\\n                \"preconditionIssue\": null\\n\\n            },\\n\\n            {'), Document(metadata={'source': 'test.docx'}, page_content='{\\n\\n                \"row\": [\\n\\n                    \"2023-09-08\",\\n\\n                    \"2023-09-09\"\\n\\n                ],'), Document(metadata={'source': 'test.docx'}, page_content='],\\n\\n                \"zhName\": \"基本编码\",\\n\\n                \"value\": \"2023-09-08\",\\n\\n                \"type\": \"测试字典类型11\",'), Document(metadata={'source': 'test.docx'}, page_content='\"preconditionIssue\": null\\n\\n            },\\n\\n            {\\n\\n                \"row\": [\\n\\n                    \"2023-09-08\",'), Document(metadata={'source': 'test.docx'}, page_content='\"2023-09-07\"\\n\\n                ],\\n\\n                \"zhName\": \"基本编码\",\\n\\n                \"value\": \"2023-09-08\",'), Document(metadata={'source': 'test.docx'}, page_content='\"type\": \"测试字典类型11\",\\n\\n                \"preconditionIssue\": null\\n\\n            },\\n\\n            {\\n\\n                \"row\": ['), Document(metadata={'source': 'test.docx'}, page_content='\"\",\\n\\n                    \"2023-09-08\"\\n\\n                ],\\n\\n                \"zhName\": \"基本编码\",\\n\\n                \"value\": \"\",'), Document(metadata={'source': 'test.docx'}, page_content='\"type\": \"测试字典类型11\",\\n\\n                \"preconditionIssue\": null\\n\\n            },\\n\\n            {\\n\\n                \"row\": ['), Document(metadata={'source': 'test.docx'}, page_content='\"2023-09-08\",\\n\\n                    \"\"\\n\\n                ],\\n\\n                \"zhName\": \"基本编码\",'), Document(metadata={'source': 'test.docx'}, page_content='\"value\": \"2023-09-08\",\\n\\n                \"type\": \"测试字典类型11\",\\n\\n                \"preconditionIssue\": null\\n\\n            }\\n\\n        ]'), Document(metadata={'source': 'test.docx'}, page_content=']\\n\\n    },\\n\\n    \"state\": 1,\\n\\n    \"code\": \"10000\",\\n\\n    \"msg\": \"成功\"\\n\\n}\\n\\n返回\\n\\n失败\\n\\n示例\\n\\n{\\n\\n    \"data\": {\\n\\n        \"status\": 400,'), Document(metadata={'source': 'test.docx'}, page_content='\"timestamp\": \"2024-04-10 16:03:00\",\\n\\n        \"message\": \"detail.taskCode: 校验任务编号不能为空\"\\n\\n    },\\n\\n    \"state\": 0,\\n\\n    \"code\": \"10002\",'), Document(metadata={'source': 'test.docx'}, page_content='\"code\": \"10002\",\\n\\n    \"msg\": \"失败\"\\n\\n}\\n\\n示例\\n\\n备注\\n\\n参数名\\n\\n类型\\n\\n参数含义\\n\\ntaskName\\n\\nstring\\n\\n任务规则名称\\n\\ntaskCode\\n\\nstring\\n\\n任务编号\\n\\nissueFields\\n\\narray\\n\\n问题字段统计'), Document(metadata={'source': 'test.docx'}, page_content='array\\n\\n问题字段统计\\n\\nissueFields.zhName\\n\\nstring\\n\\n问题字段名称\\n\\nissueFields.type\\n\\nstring\\n\\n问题类型\\n\\nissueFields.preconditionIssue\\n\\nstring\\n\\n前置条件问题类型\\n\\nissueFields.amount'), Document(metadata={'source': 'test.docx'}, page_content='issueFields.amount\\n\\nint\\n\\n问题字段数量\\n\\ntotal\\n\\nint\\n\\n校验总量\\n\\nsucessTotal\\n\\nint\\n\\n正确数据量\\n\\nissueTotal\\n\\nint\\n\\n错误数据量\\n\\nvalidationResultDetails\\n\\narray\\n\\n校验结果详情'), Document(metadata={'source': 'test.docx'}, page_content='array\\n\\n校验结果详情\\n\\nvalidationResultDetails.row\\n\\narray\\n\\n校验的数据行\\n\\nvalidationResultDetails.zhName\\n\\nstring\\n\\n问题字段名\\n\\nvalidationResultDetails.value\\n\\nstring\\n\\n字段值'), Document(metadata={'source': 'test.docx'}, page_content='string\\n\\n字段值\\n\\nvalidationResultDetails.type\\n\\nstring\\n\\n问题类型\\n\\nvalidationResultDetails.preconditionIssue\\n\\nstring\\n\\n前置条件\\n\\nhead\\n\\narray\\n\\n校验数据表头\\n\\n备注\\n\\n\\n\\t规则能力接口'), Document(metadata={'source': 'test.docx'}, page_content='校验数据表头\\n\\n备注\\n\\n\\n\\t规则能力接口\\n\\n2.1规则校验接口\\n\\nURI\\n\\nPOST /validation/api/external/rules/execute\\n\\n功能\\n\\n规则能力调用\\n\\n参数名\\n\\n参数含义\\n\\n类型\\n\\n必选\\n\\n请求体参数\\n\\n\\truleCode\\n\\n\\t规则编码\\n\\n\\tstring\\n\\n\\t是'), Document(metadata={'source': 'test.docx'}, page_content='规则编码\\n\\n\\tstring\\n\\n\\t是\\n\\n\\tvalue\\n\\n\\t被校验的值(默认值为空字符串)\\n\\n\\tstring\\n\\n\\t否\\n\\n\\tfield\\n\\n\\t字段名称\\n\\n\\tString\\n\\n\\t否\\n\\n请求示例\\n\\n[{\\n\\n\\t\\t\"ruleCode\": \"GZ0000511\",\\n\\n\\t\\t\"value\": \"100\",'), Document(metadata={'source': 'test.docx'}, page_content='\"value\": \"100\",\\n\\n\\t\\t\"field\": \"年龄\"\\n\\n\\t},\\n\\n\\t{\\n\\n\\t\\t\"ruleCode\": \"GZ0000512\",\\n\\n\\t\\t\"value\": \"张三\",\\n\\n\\t\\t\"field\": \"姓名\"\\n\\n\\t}\\n\\n]\\n\\n返回\\n\\n结果\\n\\n参数\\n\\n参数名\\n\\n参数含义\\n\\ncode\\n\\n调用结果：'), Document(metadata={'source': 'test.docx'}, page_content='参数含义\\n\\ncode\\n\\n调用结果：\\n\\n10000-成功\\n\\n10002-失败\\n\\nmsg\\n\\n查询结果描述\\n\\ndata\\n\\n查询结果\\n\\nstate\\n\\n响应状态，0-失败，1-成功\\n\\n返回\\n\\n成功\\n\\n示例\\n\\n{\\n\\n    \"data\": [{\\n\\n\\t\\t\"ruleCode\": \"GZ0000511\",'), Document(metadata={'source': 'test.docx'}, page_content='\"value\": \"100\",\\n\\n\\t\\t\"field\": \"年龄\",\\n\\n\\t\\t\"result\": 0\\n\\n\\t},\\n\\n\\t{\\n\\n\\t\\t\"ruleCode\": \"GZ0000512\",\\n\\n\\t\\t\"value\": \"张三\",\\n\\n\\t\\t\"field\": \"姓名\",\\n\\n\\t\\t\"result\": 1\\n\\n\\t}\\n\\n],'), Document(metadata={'source': 'test.docx'}, page_content='}\\n\\n],\\n\\n    \"state\": 1,\\n\\n    \"code\": \"10000\",\\n\\n    \"msg\": \"成功\"\\n\\n}\\n\\n返回\\n\\n失败\\n\\n示例\\n\\n{\\n\\n    \"data\": {\\n\\n        \"status\": 400,'), Document(metadata={'source': 'test.docx'}, page_content='\"timestamp\": \"2024-04-24 11:47:51\",\\n\\n        \"message\": \"规则不存在\"\\n\\n    },\\n\\n    \"state\": 0,\\n\\n    \"code\": \"10002\",\\n\\n    \"msg\": \"失败\"\\n\\n}\\n\\n示例\\n\\n备注'), Document(metadata={'source': 'test.docx'}, page_content='}\\n\\n示例\\n\\n备注\\n\\n参数名\\n\\n类型\\n\\n参数含义\\n\\nresult\\n\\nstring\\n\\n执行结果 0通过、1不通过\\n\\n备注')]\n"]}], "source": ["chat_doc = ChatDoc()\n", "chat_doc.getFile(\"test.docx\")  # 先加载文件\n", "chat_doc.splitSentence()       # 然后分割文本\n", "print(chat_doc.getSplitText())"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["测试Excel文件:\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[nltk_data] Error loading averaged_perceptron_tagger_eng: <urlopen\n", "[nltk_data]     error [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred\n", "[nltk_data]     in violation of protocol (_ssl.c:1028)>\n", "[nltk_data] Error loading punkt_tab: <urlopen error [SSL:\n", "[nltk_data]     UNEXPECTED_EOF_WHILE_READING] EOF occurred in\n", "[nltk_data]     violation of protocol (_ssl.c:1028)>\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Excel文件加载成功，内容长度: 15304\n", "前100个字符: 发起时间 完成时间 发起人姓名 发起人部门 请假类型 开始时间 结束时间 时长 2024-12-31 11:16:45 2024-12-31 12:07:56 王小兮 研发三部 年假 2025-01-...\n", "\n", "==================================================\n", "\n", "测试Word文件:\n", "Word文件加载成功，内容长度: 7019\n", "前100个字符: 变更历史 \n", "\n", "版本号\n", "\n", "日期\n", "\n", "作者\n", "\n", "扼要说明\n", "\n", "V0.1.1\n", "\n", "2024-04-24\n", "\n", "胡欣\n", "\n", "新建文档，新增1.1、1.2、2.1接口描述\n", "\n", "1校验结果查询相关接口\n", "\n", "1.1校验结果列表接口\n", "\n", "...\n"]}], "source": ["# 测试ChatDoc类\n", "# Test ChatDoc class\n", "chatdoc = ChatDoc()\n", "\n", "# 测试Excel文件\n", "print(\"测试Excel文件:\")\n", "excel_result = chatdoc.getFile(\"test.xlsx\")\n", "if excel_result:\n", "    print(f\"Excel文件加载成功，内容长度: {len(excel_result[0].page_content)}\")\n", "    print(f\"前100个字符: {excel_result[0].page_content[:100]}...\")\n", "else:\n", "    print(\"Excel文件加载失败\")\n", "\n", "print(\"\\n\" + \"=\"*50 + \"\\n\")\n", "\n", "# 测试Word文件\n", "print(\"测试Word文件:\")\n", "word_result = chatdoc.getFile(\"test.docx\")\n", "if word_result:\n", "    print(f\"Word文件加载成功，内容长度: {len(word_result[0].page_content)}\")\n", "    print(f\"前100个字符: {word_result[0].page_content[:100]}...\")\n", "else:\n", "    print(\"Word文件加载失败\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 安装必要的依赖\n", "! pip install langchain-chroma langchain-openai\n"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/2x/7ztnm7x5111gcvjp0x0nv6400000gn/T/ipykernel_28420/3618142978.py:16: LangChainDeprecationWarning: The class `ChatOpenAI` was deprecated in LangChain 0.0.10 and will be removed in 1.0. An updated version of the class exists in the :class:`~langchain-openai package and should be used instead. To use it run `pip install -U :class:`~langchain-openai` and import as `from :class:`~langchain_openai import ChatOpenAI``.\n", "  llm = ChatOpenAI(\n"]}], "source": ["# 查询重写提高文档检索准确性\n", "\n", "# 导入必须的包\n", "# Import the required packages\n", "from langchain_community.document_loaders import UnstructuredExcelLoader, Docx2txtLoader, PyPDFLoader\n", "from langchain_text_splitters import CharacterTextSplitter \n", "from langchain_chroma import Chroma \n", "from langchain_openai import OpenAIEmbeddings \n", "from langchain.retrievers.multi_query import MultiQueryRetriever\n", "import os\n", "\n", "# 修复：使用正确的DeepSeek导入\n", "from langchain_community.chat_models import ChatOpenAI\n", "\n", "# 使用ChatOpenAI来连接DeepSeek API\n", "llm = ChatOpenAI(\n", "    model=\"deepseek-chat\",\n", "    temperature=0,\n", "    openai_api_key=\"sk-778deaff83d44d3a864caa8f3cf46c75\",\n", "    openai_api_base=\"https://api.deepseek.com/v1\"\n", ")\n", "\n", "# 使用国产模型\n", "embeddings_model = OpenAIEmbeddings(\n", "    model=\"BAAI/bge-m3\",\n", "    api_key=\"sk-vgbdlqjvfytxctphzbbdzcfzzqhpmrjhjynftgnpdkchbjfd\",\n", "    base_url=\"https://api.siliconflow.cn/v1\"\n", ")\n", "\n", "# 定义完整的ChatDoc类\n", "class ChatDoc():\n", "    def __init__(self):\n", "        self.doc = None\n", "        self.splitText = []\n", "        self.file_path = None\n", "\n", "    def getFile(self):\n", "        doc = self.doc\n", "        if not doc:\n", "            print(\"请先设置文档路径\")\n", "            return None\n", "            \n", "        loaders = {\n", "            \"docx\": Docx2txtLoader,\n", "            \"pdf\": <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "            \"xlsx\": UnstructuredExcelLoader,\n", "        }\n", "        file_extension = doc.split(\".\")[-1].lower()\n", "        loader_class = loaders.get(file_extension)\n", "        if loader_class:\n", "            try:\n", "                loader = loader_class(doc)\n", "                text = loader.load()\n", "                return text\n", "            except Exception as e:\n", "                print(f\"Error loading {file_extension} files: {e}\")\n", "                return None\n", "        else:\n", "            print(f\"Unsupported file extension: {file_extension}\")\n", "            return None\n", "    \n", "    # 分割文本\n", "    def splitSentence(self):\n", "        full_text = self.getFile()\n", "        if full_text is not None:\n", "            text_splitter = CharacterTextSplitter(chunk_size=150, chunk_overlap=20)\n", "            texts = text_splitter.split_documents(full_text)\n", "            self.splitText = texts\n", "            print(f\"文本分割完成，共{len(texts)}个片段\")\n", "        else:\n", "            print(\"文件加载失败\")\n", "\n", "    def getSplitText(self):\n", "        return self.splitText\n", "    \n", "    # 向量化向量存储 (修复方法名)\n", "    def embeddingAndVectorDB(self):\n", "        if not self.splitText:\n", "            print(\"请先分割文本\")\n", "            return None\n", "        try:\n", "            db = Chroma.from_documents(self.splitText, embeddings_model)\n", "            return db\n", "        except Exception as e:\n", "            print(f\"向量化失败: {e}\")\n", "            return None\n", "    \n", "    # 向量化\n", "    def embedding(self):\n", "        if not self.splitText:\n", "            print(\"请先分割文本\")\n", "            return []\n", "        try:\n", "            return embeddings_model.embed_documents([doc.page_content for doc in self.splitText])\n", "        except Exception as e:\n", "            print(f\"向量化失败: {e}\")\n", "            return []\n", "    \n", "    # 向量检索\n", "    def vectorSearch(self, query, k=5):\n", "        db = self.embeddingAndVectorDB()\n", "        if db:\n", "            return db.similarity_search(query, k)\n", "        return []\n", "    \n", "    # 提问并找到相关文本块，使用简单的检索 (修复方法名)\n", "    def askAndFindFiles(self, query):\n", "        db = self.embeddingAndVectorDB()\n", "        if not db:\n", "            return []\n", "            \n", "        try:\n", "            # 把问题交给LLM进行多角度的扩展\n", "            retriever_from_llm = MultiQueryRetriever(\n", "                retriever=db.as_retriever(),\n", "                llm=llm,\n", "            )\n", "            results = retriever_from_llm.invoke(query)\n", "            return results\n", "        except Exception as e:\n", "            print(f\"多查询检索失败: {e}\")\n", "            print(\"使用简单检索作为备选\")\n", "            return self.vectorSearch(query)\n"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Created a chunk of size 181, which is longer than the specified 150\n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== 测试修复后的ChatDoc类 ===\n", "1. 分割文本...\n", "文本分割完成，共54个片段\n", "\n", "2. 测试向量检索...\n", "找到 3 个相关文档片段\n", "片段 1: {\n", "\n", "                \"enName\": \"zwfwCatalogCode\",\n", "\n", "                \"zhName\": \"基本编码\",\n", "\n", "                ...\n", "片段 2: {\n", "\n", "                \"enName\": \"zwfwCatalogCode\",\n", "\n", "                \"zhName\": \"基本编码\",\n", "\n", "                ...\n", "片段 3: {\n", "\n", "                \"enName\": \"zwfwCatalogCode\",\n", "\n", "                \"zhName\": \"基本编码\",\n", "\n", "                ...\n", "\n", "3. 测试多查询检索...\n", "多查询检索失败: 1 validation error for MultiQueryRetriever\n", "llm_chain\n", "  Field required [type=missing, input_value={'retriever': VectorStore...m/v1', openai_proxy='')}, input_type=dict]\n", "    For further information visit https://errors.pydantic.dev/2.11/v/missing\n", "使用简单检索作为备选\n", "多查询检索找到 5 个相关文档片段\n", "片段 1: {\n", "\n", "                \"enName\": \"zwfwCatalogCode\",\n", "\n", "                \"zhName\": \"基本编码\",\n", "\n", "                ...\n", "片段 2: {\n", "\n", "                \"enName\": \"zwfwCatalogCode\",\n", "\n", "                \"zhName\": \"基本编码\",\n", "\n", "                ...\n", "片段 3: {\n", "\n", "                \"enName\": \"zwfwCatalogCode\",\n", "\n", "                \"zhName\": \"基本编码\",\n", "\n", "                ...\n", "片段 4: {\n", "\n", "                \"enName\": \"zwfwCatalogCode\",\n", "\n", "                \"zhName\": \"基本编码\",\n", "\n", "                ...\n", "片段 5: {\n", "\n", "                \"enName\": \"zwfwCatalogCode\",\n", "\n", "                \"zhName\": \"基本编码\",\n", "\n", "                ...\n", "\n", "=== 测试完成 ===\n"]}], "source": ["# 测试修复后的ChatDoc类\n", "print(\"=== 测试修复后的ChatDoc类 ===\")\n", "\n", "# 创建ChatDoc实例\n", "chat_doc = ChatDoc()\n", "chat_doc.doc = \"test.docx\"\n", "\n", "# 分割文本\n", "print(\"1. 分割文本...\")\n", "chat_doc.splitSentence()\n", "\n", "# 测试向量检索\n", "print(\"\\n2. 测试向量检索...\")\n", "try:\n", "    results = chat_doc.vectorSearch(\"版本号是多少？\", k=3)\n", "    print(f\"找到 {len(results)} 个相关文档片段\")\n", "    for i, doc in enumerate(results):\n", "        print(f\"片段 {i+1}: {doc.page_content[:100]}...\")\n", "except Exception as e:\n", "    print(f\"向量检索失败: {e}\")\n", "\n", "# 测试多查询检索\n", "print(\"\\n3. 测试多查询检索...\")\n", "try:\n", "    results = chat_doc.askAndFindFiles(\"版本号是多少？\")\n", "    print(f\"多查询检索找到 {len(results)} 个相关文档片段\")\n", "    for i, doc in enumerate(results):\n", "        print(f\"片段 {i+1}: {doc.page_content[:100]}...\")\n", "except Exception as e:\n", "    print(f\"多查询检索失败: {e}\")\n", "\n", "print(\"\\n=== 测试完成 ===\")\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting unstructured[xlsx]\n", "  Downloading unstructured-0.17.2-py3-none-any.whl.metadata (24 kB)\n", "Collecting chardet (from unstructured[xlsx])\n", "  Downloading chardet-5.2.0-py3-none-any.whl.metadata (3.4 kB)\n", "Collecting filetype (from unstructured[xlsx])\n", "  Downloading filetype-1.2.0-py2.py3-none-any.whl.metadata (6.5 kB)\n", "Collecting python-magic (from unstructured[xlsx])\n", "  Downloading python_magic-0.4.27-py2.py3-none-any.whl.metadata (5.8 kB)\n", "Collecting lxml (from unstructured[xlsx])\n", "  Downloading lxml-5.4.0-cp313-cp313-macosx_10_13_universal2.whl.metadata (3.5 kB)\n", "Collecting nltk (from unstructured[xlsx])\n", "  Downloading nltk-3.9.1-py3-none-any.whl.metadata (2.9 kB)\n", "Requirement already satisfied: requests in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from unstructured[xlsx]) (2.32.3)\n", "Requirement already satisfied: beautifulsoup4 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from unstructured[xlsx]) (4.13.4)\n", "Collecting emoji (from unstructured[xlsx])\n", "  Downloading emoji-2.14.1-py3-none-any.whl.metadata (5.7 kB)\n", "Requirement already satisfied: dataclasses-json in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from unstructured[xlsx]) (0.6.7)\n", "Collecting python-iso639 (from unstructured[xlsx])\n", "  Downloading python_iso639-2025.2.18-py3-none-any.whl.metadata (14 kB)\n", "Collecting langdetect (from unstructured[xlsx])\n", "  Downloading langdetect-1.0.9.tar.gz (981 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m981.5/981.5 kB\u001b[0m \u001b[31m11.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25ldone\n", "\u001b[?25hRequirement already satisfied: numpy in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from unstructured[xlsx]) (2.2.4)\n", "Collecting rapidfuzz (from unstructured[xlsx])\n", "  Downloading rapidfuzz-3.13.0-cp313-cp313-macosx_11_0_arm64.whl.metadata (12 kB)\n", "Collecting backoff (from unstructured[xlsx])\n", "  Downloading backoff-2.2.1-py3-none-any.whl.metadata (14 kB)\n", "Requirement already satisfied: typing-extensions in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from unstructured[xlsx]) (4.13.2)\n", "Collecting unstructured-client (from unstructured[xlsx])\n", "  Downloading unstructured_client-0.36.0-py3-none-any.whl.metadata (21 kB)\n", "Collecting wrapt (from unstructured[xlsx])\n", "  Downloading wrapt-1.17.2-cp313-cp313-macosx_11_0_arm64.whl.metadata (6.4 kB)\n", "Requirement already satisfied: tqdm in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from unstructured[xlsx]) (4.67.1)\n", "Requirement already satisfied: psutil in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from unstructured[xlsx]) (7.0.0)\n", "Collecting python-oxmsg (from unstructured[xlsx])\n", "  Downloading python_oxmsg-0.0.2-py3-none-any.whl.metadata (5.0 kB)\n", "Collecting html5lib (from unstructured[xlsx])\n", "  Downloading html5lib-1.1-py2.py3-none-any.whl.metadata (16 kB)\n", "Requirement already satisfied: openpyxl in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from unstructured[xlsx]) (3.1.5)\n", "Requirement already satisfied: pandas in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from unstructured[xlsx]) (2.3.0)\n", "Collecting xlrd (from unstructured[xlsx])\n", "  Downloading xlrd-2.0.2-py2.py3-none-any.whl.metadata (3.5 kB)\n", "Collecting networkx (from unstructured[xlsx])\n", "  Downloading networkx-3.5-py3-none-any.whl.metadata (6.3 kB)\n", "Requirement already satisfied: soupsieve>1.2 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from beautifulsoup4->unstructured[xlsx]) (2.7)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from dataclasses-json->unstructured[xlsx]) (3.26.1)\n", "Requirement already satisfied: typing-inspect<1,>=0.4.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from dataclasses-json->unstructured[xlsx]) (0.9.0)\n", "Requirement already satisfied: six>=1.9 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from html5lib->unstructured[xlsx]) (1.17.0)\n", "Requirement already satisfied: webencodings in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from html5lib->unstructured[xlsx]) (0.5.1)\n", "Collecting click (from nltk->unstructured[xlsx])\n", "  Downloading click-8.2.1-py3-none-any.whl.metadata (2.5 kB)\n", "Collecting joblib (from nltk->unstructured[xlsx])\n", "  Downloading joblib-1.5.1-py3-none-any.whl.metadata (5.6 kB)\n", "Requirement already satisfied: regex>=2021.8.3 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from nltk->unstructured[xlsx]) (2024.11.6)\n", "Requirement already satisfied: et-xmlfile in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from openpyxl->unstructured[xlsx]) (2.0.0)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from pandas->unstructured[xlsx]) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from pandas->unstructured[xlsx]) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from pandas->unstructured[xlsx]) (2025.2)\n", "Collecting olefile (from python-oxmsg->unstructured[xlsx])\n", "  Downloading olefile-0.47-py2.py3-none-any.whl.metadata (9.7 kB)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from requests->unstructured[xlsx]) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from requests->unstructured[xlsx]) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from requests->unstructured[xlsx]) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from requests->unstructured[xlsx]) (2025.4.26)\n", "Collecting aiofiles>=24.1.0 (from unstructured-client->unstructured[xlsx])\n", "  Downloading aiofiles-24.1.0-py3-none-any.whl.metadata (10 kB)\n", "Requirement already satisfied: cryptography>=3.1 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from unstructured-client->unstructured[xlsx]) (44.0.2)\n", "Requirement already satisfied: httpx>=0.27.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from unstructured-client->unstructured[xlsx]) (0.28.1)\n", "Requirement already satisfied: nest-asyncio>=1.6.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from unstructured-client->unstructured[xlsx]) (1.6.0)\n", "Requirement already satisfied: pydantic>=2.11.2 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from unstructured-client->unstructured[xlsx]) (2.11.3)\n", "Collecting pypdf>=4.0 (from unstructured-client->unstructured[xlsx])\n", "  Downloading pypdf-5.6.0-py3-none-any.whl.metadata (7.2 kB)\n", "Requirement already satisfied: requests-toolbelt>=1.0.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from unstructured-client->unstructured[xlsx]) (1.0.0)\n", "Requirement already satisfied: cffi>=1.12 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from cryptography>=3.1->unstructured-client->unstructured[xlsx]) (1.17.1)\n", "Requirement already satisfied: anyio in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from httpx>=0.27.0->unstructured-client->unstructured[xlsx]) (4.9.0)\n", "Requirement already satisfied: httpcore==1.* in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from httpx>=0.27.0->unstructured-client->unstructured[xlsx]) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from httpcore==1.*->httpx>=0.27.0->unstructured-client->unstructured[xlsx]) (0.16.0)\n", "Requirement already satisfied: packaging>=17.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from marshmallow<4.0.0,>=3.18.0->dataclasses-json->unstructured[xlsx]) (24.2)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from pydantic>=2.11.2->unstructured-client->unstructured[xlsx]) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.1 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from pydantic>=2.11.2->unstructured-client->unstructured[xlsx]) (2.33.1)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from pydantic>=2.11.2->unstructured-client->unstructured[xlsx]) (0.4.0)\n", "Requirement already satisfied: mypy-extensions>=0.3.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from typing-inspect<1,>=0.4.0->dataclasses-json->unstructured[xlsx]) (1.1.0)\n", "Requirement already satisfied: pycparser in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from cffi>=1.12->cryptography>=3.1->unstructured-client->unstructured[xlsx]) (2.22)\n", "Requirement already satisfied: sniffio>=1.1 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from anyio->httpx>=0.27.0->unstructured-client->unstructured[xlsx]) (1.3.1)\n", "Downloading backoff-2.2.1-py3-none-any.whl (15 kB)\n", "Downloading chardet-5.2.0-py3-none-any.whl (199 kB)\n", "Downloading emoji-2.14.1-py3-none-any.whl (590 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m590.6/590.6 kB\u001b[0m \u001b[31m21.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading filetype-1.2.0-py2.py3-none-any.whl (19 kB)\n", "Downloading html5lib-1.1-py2.py3-none-any.whl (112 kB)\n", "Downloading lxml-5.4.0-cp313-cp313-macosx_10_13_universal2.whl (8.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m8.1/8.1 MB\u001b[0m \u001b[31m48.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading networkx-3.5-py3-none-any.whl (2.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.0/2.0 MB\u001b[0m \u001b[31m53.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nltk-3.9.1-py3-none-any.whl (1.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.5/1.5 MB\u001b[0m \u001b[31m49.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading python_iso639-2025.2.18-py3-none-any.whl (167 kB)\n", "Downloading python_magic-0.4.27-py2.py3-none-any.whl (13 kB)\n", "Downloading python_oxmsg-0.0.2-py3-none-any.whl (31 kB)\n", "Downloading rapidfuzz-3.13.0-cp313-cp313-macosx_11_0_arm64.whl (1.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.4/1.4 MB\u001b[0m \u001b[31m53.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading unstructured-0.17.2-py3-none-any.whl (1.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.8/1.8 MB\u001b[0m \u001b[31m7.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hDownloading unstructured_client-0.36.0-py3-none-any.whl (195 kB)\n", "Downloading wrapt-1.17.2-cp313-cp313-macosx_11_0_arm64.whl (38 kB)\n", "Downloading xlrd-2.0.2-py2.py3-none-any.whl (96 kB)\n", "Downloading aiofiles-24.1.0-py3-none-any.whl (15 kB)\n", "Downloading pypdf-5.6.0-py3-none-any.whl (304 kB)\n", "Downloading click-8.2.1-py3-none-any.whl (102 kB)\n", "Downloading joblib-1.5.1-py3-none-any.whl (307 kB)\n", "Downloading olefile-0.47-py2.py3-none-any.whl (114 kB)\n", "Building wheels for collected packages: langdetect\n", "  Building wheel for langdetect (setup.py) ... \u001b[?25ldone\n", "\u001b[?25h  Created wheel for langdetect: filename=langdetect-1.0.9-py3-none-any.whl size=993221 sha256=0596098e8a1259e4bef55a8e176d08549424e4295ffeeb6a8eb251a1f0befc38\n", "  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/eb/87/25/2dddf1c94e1786054e25022ec5530bfed52bad86d882999c48\n", "Successfully built langdetect\n", "Installing collected packages: filetype, xlrd, wrapt, rapidfuzz, python-magic, python-iso639, pypdf, olefile, networkx, lxml, langdetect, joblib, html5lib, emoji, click, chardet, backoff, aiofiles, python-oxmsg, nltk, unstructured-client, unstructured\n", "Successfully installed aiofiles-24.1.0 backoff-2.2.1 chardet-5.2.0 click-8.2.1 emoji-2.14.1 filetype-1.2.0 html5lib-1.1 joblib-1.5.1 langdetect-1.0.9 lxml-5.4.0 networkx-3.5 nltk-3.9.1 olefile-0.47 pypdf-5.6.0 python-iso639-2025.2.18 python-magic-0.4.27 python-oxmsg-0.0.2 rapidfuzz-3.13.0 unstructured-0.17.2 unstructured-client-0.36.0 wrapt-1.17.2 xlrd-2.0.2\n"]}], "source": ["! pip install \"unstructured[xlsx]\"\n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Created a chunk of size 181, which is longer than the specified 150\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[Document(metadata={'source': 'test.docx'}, page_content='变更历史 \\n\\n版本号\\n\\n日期\\n\\n作者\\n\\n扼要说明\\n\\nV0.1.1\\n\\n2024-04-24\\n\\n胡欣\\n\\n新建文档，新增1.1、1.2、2.1接口描述\\n\\n1校验结果查询相关接口\\n\\n1.1校验结果列表接口\\n\\nURI\\n\\nGET /validation/api/external/results/list\\n\\n功能'), Document(metadata={'source': 'test.docx'}, page_content='功能\\n\\n获取校验结果列表\\n\\n参数名\\n\\n参数含义\\n\\n类型\\n\\n必选\\n\\nURL参数\\n\\n\\ttaskCode\\n\\n\\t校验规则编号\\n\\n\\tstring\\n\\n\\t是\\n\\n\\tstartExecTime\\n\\n\\t执行时间开始时间(yyyy-MM-dd HH:mm:ss)\\n\\n\\tdate\\n\\n\\t否\\n\\n\\tendExecTime'), Document(metadata={'source': 'test.docx'}, page_content='否\\n\\n\\tendExecTime\\n\\n\\t执行时间结束时间(yyyy-MM-dd HH:mm:ss)\\n\\n\\tdate\\n\\n\\t否\\n\\n\\tpreconditionIssueType\\n\\n\\t前置条件问题类型\\n\\n\\tstring\\n\\n\\t否\\n\\n\\tissueType\\n\\n\\t问题类型\\n\\n\\tstring\\n\\n\\t否\\n\\n\\tpage'), Document(metadata={'source': 'test.docx'}, page_content='string\\n\\n\\t否\\n\\n\\tpage\\n\\n\\t分页数（默认0）\\n\\n\\tint\\n\\n\\t否\\n\\n\\tsize\\n\\n\\t分页大小(默认10)\\n\\n\\tint\\n\\n\\t否\\n\\n请求示例'), Document(metadata={'source': 'test.docx'}, page_content='http://192.168.2.26:8051/validation/api/external/results/list?startExecTime=2024-03-11%2012:34:33&endExecTime=2024-03-13%2012:34:34&preconditionIssueType=GZBZQ&taskCode=BSZNJY000055'), Document(metadata={'source': 'test.docx'}, page_content='返回\\n\\n结果\\n\\n参数\\n\\n参数名\\n\\n参数含义\\n\\ncode\\n\\n调用结果：\\n\\n10000-成功\\n\\n10002-失败\\n\\nmsg\\n\\n查询结果描述\\n\\ndata\\n\\n查询结果\\n\\nstate\\n\\n响应状态，0-失败，1-成功\\n\\n返回\\n\\n成功\\n\\n示例\\n\\n{\\n\\n    \"data\": {'), Document(metadata={'source': 'test.docx'}, page_content='示例\\n\\n{\\n\\n    \"data\": {\\n\\n        \"content\": [\\n\\n            {\\n\\n                \"taskCode\": \"BSZNJY000055-0004\",\\n\\n                \"taskName\": \"测试222\",'), Document(metadata={'source': 'test.docx'}, page_content='\"executionTime\": \"2024-03-12 12:34:33\",\\n\\n                \"issueFields\": [\\n\\n                    {'), Document(metadata={'source': 'test.docx'}, page_content='\"enName\": null,\\n\\n                        \"zhName\": \"数学111\",\\n\\n                        \"type\": null,'), Document(metadata={'source': 'test.docx'}, page_content='\"preconditionIssue\": \"该值不准确\",\\n\\n                        \"amount\": \"1\"\\n\\n                    }\\n\\n                ],'), Document(metadata={'source': 'test.docx'}, page_content='],\\n\\n                \"total\": 1,\\n\\n                \"issueTotal\": 1,\\n\\n                \"successTotal\": 0\\n\\n            }\\n\\n        ],'), Document(metadata={'source': 'test.docx'}, page_content='],\\n\\n        \"pageable\": {\\n\\n            \"sort\": {\\n\\n                \"sorted\": true,\\n\\n                \"unsorted\": false,'), Document(metadata={'source': 'test.docx'}, page_content='\"empty\": false\\n\\n            },\\n\\n            \"pageNumber\": 0,\\n\\n            \"pageSize\": 1,\\n\\n            \"offset\": 0,'), Document(metadata={'source': 'test.docx'}, page_content='\"unpaged\": false,\\n\\n            \"paged\": true\\n\\n        },\\n\\n        \"totalPages\": 3,\\n\\n        \"totalElements\": 3,\\n\\n        \"last\": false,'), Document(metadata={'source': 'test.docx'}, page_content='\"first\": true,\\n\\n        \"sort\": {\\n\\n            \"sorted\": true,\\n\\n            \"unsorted\": false,\\n\\n            \"empty\": false\\n\\n        },'), Document(metadata={'source': 'test.docx'}, page_content='},\\n\\n        \"numberOfElements\": 1,\\n\\n        \"size\": 1,\\n\\n        \"number\": 0,\\n\\n        \"empty\": false\\n\\n    },\\n\\n    \"state\": 1,'), Document(metadata={'source': 'test.docx'}, page_content='\"state\": 1,\\n\\n    \"code\": \"10000\",\\n\\n    \"msg\": \"成功\"\\n\\n}\\n\\n返回\\n\\n失败\\n\\n示例\\n\\n{\\n\\n    \"data\": {\\n\\n        \"status\": 400,'), Document(metadata={'source': 'test.docx'}, page_content='\"timestamp\": \"2024-04-10 15:50:55\",\\n\\n        \"message\": \"taskCode:编码不能为空\"\\n\\n    },\\n\\n    \"state\": 0,\\n\\n    \"code\": \"10002\",\\n\\n    \"msg\": \"失败\"\\n\\n}'), Document(metadata={'source': 'test.docx'}, page_content='\"msg\": \"失败\"\\n\\n}\\n\\n示例\\n\\n备注\\n\\n参数名\\n\\n类型\\n\\n参数含义\\n\\ntaskCode\\n\\nstring\\n\\n任务规则编号\\n\\ntaskName\\n\\nstring\\n\\n任务规则名称\\n\\nexecutionTime\\n\\ndate\\n\\n执行时间\\n\\nissueFields\\n\\narray\\n\\n问题字段统计'), Document(metadata={'source': 'test.docx'}, page_content='array\\n\\n问题字段统计\\n\\nissueFields.zhName\\n\\nstring\\n\\n问题字段名称\\n\\nissueFields.type\\n\\nstring\\n\\n问题类型\\n\\nissueFields.preconditionIssue\\n\\nstring\\n\\n前置条件问题类型\\n\\nissueFields.amount'), Document(metadata={'source': 'test.docx'}, page_content='issueFields.amount\\n\\nint\\n\\n问题字段数量\\n\\ntotal\\n\\nint\\n\\n校验总量\\n\\nsucessTotal\\n\\nint\\n\\n正确数据量\\n\\nissueTotal\\n\\nint\\n\\n错误数据量\\n\\n备注\\n\\n1.2校验结果详情接口\\n\\nURI'), Document(metadata={'source': 'test.docx'}, page_content='备注\\n\\n1.2校验结果详情接口\\n\\nURI\\n\\nGET /validation/api/external/results/detail\\n\\n功能\\n\\n获取校验接口详情\\n\\n参数名\\n\\n参数含义\\n\\n类型\\n\\n必选\\n\\nURL参数\\n\\n\\ttaskCode\\n\\n\\t校验任务编号\\n\\n\\tstring\\n\\n\\t是\\n\\n请求示例'), Document(metadata={'source': 'test.docx'}, page_content='string\\n\\n\\t是\\n\\n请求示例\\n\\nhttp://192.168.2.26:8051/validation/api/external/results/detail?taskCode=BSZNJY000055-0004\\n\\n返回\\n\\n结果\\n\\n参数\\n\\n参数名\\n\\n参数含义\\n\\ncode\\n\\n调用结果：'), Document(metadata={'source': 'test.docx'}, page_content='参数含义\\n\\ncode\\n\\n调用结果：\\n\\n10000-成功\\n\\n10002-失败\\n\\nmsg\\n\\n查询结果描述\\n\\ndata\\n\\n查询结果\\n\\nstate\\n\\n响应状态，0-失败，1-成功\\n\\n返回\\n\\n成功\\n\\n示例\\n\\n{\\n\\n    \"data\": {\\n\\n        \"taskName\": \"基本编码校验\",'), Document(metadata={'source': 'test.docx'}, page_content='\"total\": 5,\\n\\n        \"successTotal\": 0,\\n\\n        \"issueTotal\": 5,\\n\\n        \"issueFields\": [\\n\\n            {'), Document(metadata={'source': 'test.docx'}, page_content='{\\n\\n                \"enName\": \"zwfwCatalogCode\",\\n\\n                \"zhName\": \"基本编码\",\\n\\n                \"type\": \"测试字典类型11\",'), Document(metadata={'source': 'test.docx'}, page_content='\"preconditionIssue\": null,\\n\\n                \"amount\": \"5\"\\n\\n            }\\n\\n        ],\\n\\n        \"head\": [\\n\\n            \"基本编码\",'), Document(metadata={'source': 'test.docx'}, page_content='\"基本编码\",\\n\\n            \"证件类型\"\\n\\n        ],\\n\\n        \"validationResultDetails\": [\\n\\n            {\\n\\n                \"row\": ['), Document(metadata={'source': 'test.docx'}, page_content='\"2023-09-08\",\\n\\n                    \"2023-09-08\"\\n\\n                ],\\n\\n                \"zhName\": \"基本编码\",'), Document(metadata={'source': 'test.docx'}, page_content='\"value\": \"2023-09-08\",\\n\\n                \"type\": \"测试字典类型11\",\\n\\n                \"preconditionIssue\": null\\n\\n            },\\n\\n            {'), Document(metadata={'source': 'test.docx'}, page_content='{\\n\\n                \"row\": [\\n\\n                    \"2023-09-08\",\\n\\n                    \"2023-09-09\"\\n\\n                ],'), Document(metadata={'source': 'test.docx'}, page_content='],\\n\\n                \"zhName\": \"基本编码\",\\n\\n                \"value\": \"2023-09-08\",\\n\\n                \"type\": \"测试字典类型11\",'), Document(metadata={'source': 'test.docx'}, page_content='\"preconditionIssue\": null\\n\\n            },\\n\\n            {\\n\\n                \"row\": [\\n\\n                    \"2023-09-08\",'), Document(metadata={'source': 'test.docx'}, page_content='\"2023-09-07\"\\n\\n                ],\\n\\n                \"zhName\": \"基本编码\",\\n\\n                \"value\": \"2023-09-08\",'), Document(metadata={'source': 'test.docx'}, page_content='\"type\": \"测试字典类型11\",\\n\\n                \"preconditionIssue\": null\\n\\n            },\\n\\n            {\\n\\n                \"row\": ['), Document(metadata={'source': 'test.docx'}, page_content='\"\",\\n\\n                    \"2023-09-08\"\\n\\n                ],\\n\\n                \"zhName\": \"基本编码\",\\n\\n                \"value\": \"\",'), Document(metadata={'source': 'test.docx'}, page_content='\"type\": \"测试字典类型11\",\\n\\n                \"preconditionIssue\": null\\n\\n            },\\n\\n            {\\n\\n                \"row\": ['), Document(metadata={'source': 'test.docx'}, page_content='\"2023-09-08\",\\n\\n                    \"\"\\n\\n                ],\\n\\n                \"zhName\": \"基本编码\",'), Document(metadata={'source': 'test.docx'}, page_content='\"value\": \"2023-09-08\",\\n\\n                \"type\": \"测试字典类型11\",\\n\\n                \"preconditionIssue\": null\\n\\n            }\\n\\n        ]'), Document(metadata={'source': 'test.docx'}, page_content=']\\n\\n    },\\n\\n    \"state\": 1,\\n\\n    \"code\": \"10000\",\\n\\n    \"msg\": \"成功\"\\n\\n}\\n\\n返回\\n\\n失败\\n\\n示例\\n\\n{\\n\\n    \"data\": {\\n\\n        \"status\": 400,'), Document(metadata={'source': 'test.docx'}, page_content='\"timestamp\": \"2024-04-10 16:03:00\",\\n\\n        \"message\": \"detail.taskCode: 校验任务编号不能为空\"\\n\\n    },\\n\\n    \"state\": 0,\\n\\n    \"code\": \"10002\",'), Document(metadata={'source': 'test.docx'}, page_content='\"code\": \"10002\",\\n\\n    \"msg\": \"失败\"\\n\\n}\\n\\n示例\\n\\n备注\\n\\n参数名\\n\\n类型\\n\\n参数含义\\n\\ntaskName\\n\\nstring\\n\\n任务规则名称\\n\\ntaskCode\\n\\nstring\\n\\n任务编号\\n\\nissueFields\\n\\narray\\n\\n问题字段统计'), Document(metadata={'source': 'test.docx'}, page_content='array\\n\\n问题字段统计\\n\\nissueFields.zhName\\n\\nstring\\n\\n问题字段名称\\n\\nissueFields.type\\n\\nstring\\n\\n问题类型\\n\\nissueFields.preconditionIssue\\n\\nstring\\n\\n前置条件问题类型\\n\\nissueFields.amount'), Document(metadata={'source': 'test.docx'}, page_content='issueFields.amount\\n\\nint\\n\\n问题字段数量\\n\\ntotal\\n\\nint\\n\\n校验总量\\n\\nsucessTotal\\n\\nint\\n\\n正确数据量\\n\\nissueTotal\\n\\nint\\n\\n错误数据量\\n\\nvalidationResultDetails\\n\\narray\\n\\n校验结果详情'), Document(metadata={'source': 'test.docx'}, page_content='array\\n\\n校验结果详情\\n\\nvalidationResultDetails.row\\n\\narray\\n\\n校验的数据行\\n\\nvalidationResultDetails.zhName\\n\\nstring\\n\\n问题字段名\\n\\nvalidationResultDetails.value\\n\\nstring\\n\\n字段值'), Document(metadata={'source': 'test.docx'}, page_content='string\\n\\n字段值\\n\\nvalidationResultDetails.type\\n\\nstring\\n\\n问题类型\\n\\nvalidationResultDetails.preconditionIssue\\n\\nstring\\n\\n前置条件\\n\\nhead\\n\\narray\\n\\n校验数据表头\\n\\n备注\\n\\n\\n\\t规则能力接口'), Document(metadata={'source': 'test.docx'}, page_content='校验数据表头\\n\\n备注\\n\\n\\n\\t规则能力接口\\n\\n2.1规则校验接口\\n\\nURI\\n\\nPOST /validation/api/external/rules/execute\\n\\n功能\\n\\n规则能力调用\\n\\n参数名\\n\\n参数含义\\n\\n类型\\n\\n必选\\n\\n请求体参数\\n\\n\\truleCode\\n\\n\\t规则编码\\n\\n\\tstring\\n\\n\\t是'), Document(metadata={'source': 'test.docx'}, page_content='规则编码\\n\\n\\tstring\\n\\n\\t是\\n\\n\\tvalue\\n\\n\\t被校验的值(默认值为空字符串)\\n\\n\\tstring\\n\\n\\t否\\n\\n\\tfield\\n\\n\\t字段名称\\n\\n\\tString\\n\\n\\t否\\n\\n请求示例\\n\\n[{\\n\\n\\t\\t\"ruleCode\": \"GZ0000511\",\\n\\n\\t\\t\"value\": \"100\",'), Document(metadata={'source': 'test.docx'}, page_content='\"value\": \"100\",\\n\\n\\t\\t\"field\": \"年龄\"\\n\\n\\t},\\n\\n\\t{\\n\\n\\t\\t\"ruleCode\": \"GZ0000512\",\\n\\n\\t\\t\"value\": \"张三\",\\n\\n\\t\\t\"field\": \"姓名\"\\n\\n\\t}\\n\\n]\\n\\n返回\\n\\n结果\\n\\n参数\\n\\n参数名\\n\\n参数含义\\n\\ncode\\n\\n调用结果：'), Document(metadata={'source': 'test.docx'}, page_content='参数含义\\n\\ncode\\n\\n调用结果：\\n\\n10000-成功\\n\\n10002-失败\\n\\nmsg\\n\\n查询结果描述\\n\\ndata\\n\\n查询结果\\n\\nstate\\n\\n响应状态，0-失败，1-成功\\n\\n返回\\n\\n成功\\n\\n示例\\n\\n{\\n\\n    \"data\": [{\\n\\n\\t\\t\"ruleCode\": \"GZ0000511\",'), Document(metadata={'source': 'test.docx'}, page_content='\"value\": \"100\",\\n\\n\\t\\t\"field\": \"年龄\",\\n\\n\\t\\t\"result\": 0\\n\\n\\t},\\n\\n\\t{\\n\\n\\t\\t\"ruleCode\": \"GZ0000512\",\\n\\n\\t\\t\"value\": \"张三\",\\n\\n\\t\\t\"field\": \"姓名\",\\n\\n\\t\\t\"result\": 1\\n\\n\\t}\\n\\n],'), Document(metadata={'source': 'test.docx'}, page_content='}\\n\\n],\\n\\n    \"state\": 1,\\n\\n    \"code\": \"10000\",\\n\\n    \"msg\": \"成功\"\\n\\n}\\n\\n返回\\n\\n失败\\n\\n示例\\n\\n{\\n\\n    \"data\": {\\n\\n        \"status\": 400,'), Document(metadata={'source': 'test.docx'}, page_content='\"timestamp\": \"2024-04-24 11:47:51\",\\n\\n        \"message\": \"规则不存在\"\\n\\n    },\\n\\n    \"state\": 0,\\n\\n    \"code\": \"10002\",\\n\\n    \"msg\": \"失败\"\\n\\n}\\n\\n示例\\n\\n备注'), Document(metadata={'source': 'test.docx'}, page_content='}\\n\\n示例\\n\\n备注\\n\\n参数名\\n\\n类型\\n\\n参数含义\\n\\nresult\\n\\nstring\\n\\n执行结果 0通过、1不通过\\n\\n备注')]\n"]}, {"data": {"text/plain": ["<langchain_chroma.vectorstores.Chroma at 0x10d76df90>"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["#导入必须的包\n", "# Import therequired packages\n", "from langchain_community.document_loaders import UnstructuredExcelLoader,Docx2txtLoader, PyPDFLoader\n", "from langchain_text_splitters import CharacterTextSplitter \n", "from langchain_chroma import Chroma \n", "from langchain_openai import OpenAIEmbeddings \n", "import os\n", "\n", "# 使用国产模型\n", "embeddings_model = OpenAIEmbeddings(model=\"BAAI/bge-m3\",api_key=\"sk-vgbdlqjvfytxctphzbbdzcfzzqhpmrjhjynftgnpdkchbjfd\",base_url=\"https://api.siliconflow.cn/v1\")\n", "\n", "# 定义chatdoc\n", "\n", "class ChatDoc():\n", "    def __init__(self):\n", "        self.doc = None\n", "        self.splitText = []\n", "        self.file_path = None\n", "\n", "    def getFile(self,doc):\n", "        self.file_path = doc\n", "        loaders = {\n", "            \"docx\":Docx2txtLoader,\n", "            \"pdf\":<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "            \"xlsx\":UnstructuredExcelLoader,\n", "        }\n", "        file_extension = doc.split(\".\")[-1]\n", "        loader_class = loaders.get(file_extension)\n", "        if loader_class:\n", "            try:\n", "                loader = loader_class(doc)\n", "                text = loader.load()\n", "                return text\n", "            except Exception as e:\n", "                print(f\"Error loading {file_extension} files:{e}\")\n", "        else:\n", "            print(f\"Unsupported file extension: {file_extension}\")\n", "            return None\n", "    # 分割文本\n", "    def splitSentence(self):\n", "        full_text = self.getFile(self.file_path)\n", "        if full_text != None:\n", "            text_splitter = CharacterTextSplitter(chunk_size=150, chunk_overlap=20)\n", "            texts = text_splitter.split_documents(full_text)\n", "            self.splitText = texts\n", "        else:\n", "            print(\"文件加载失败\")\n", "\n", "    def getSplitText(self):\n", "        return self.splitText\n", "    #向量化向量存贮\n", "    def embeddingAmdVectorDB(self):\n", "        db = Chroma.from_documents(self.splitText,embeddings_model)\n", "        return db\n", "    # 向量化\n", "    def embedding(self):\n", "        return embeddings_model.embed_documents(self.splitText)\n", "    # 向量检索\n", "    def vectorSearch(self,query,k=5):\n", "        db = self.embeddingAmdVectorDB()\n", "        return db.similarity_search(query,k)\n", "\n", "chat_doc = ChatDoc()\n", "chat_doc.getFile(\"test.docx\")  # 先加载文件\n", "chat_doc.splitSentence()       # 然后分割文本\n", "print(chat_doc.getSplitText())\n", "chat_doc.embeddingAmdVectorDB()\n", "\n", "\n", "# 测试ChatDoc类\n", "# Test ChatDoc class"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting langchain-chroma\n", "  Downloading langchain_chroma-0.2.4-py3-none-any.whl.metadata (1.1 kB)\n", "Collecting chromadb\n", "  Downloading chromadb-1.0.12-cp39-abi3-macosx_11_0_arm64.whl.metadata (6.9 kB)\n", "Collecting langchain-core>=0.3.60 (from langchain-chroma)\n", "  Downloading langchain_core-0.3.65-py3-none-any.whl.metadata (5.8 kB)\n", "Requirement already satisfied: numpy>=2.1.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langchain-chroma) (2.2.4)\n", "Collecting build>=1.0.3 (from chromadb)\n", "  Downloading build-1.2.2.post1-py3-none-any.whl.metadata (6.5 kB)\n", "Requirement already satisfied: pydantic>=1.9 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from chromadb) (2.11.3)\n", "Collecting fastapi==0.115.9 (from chromadb)\n", "  Downloading fastapi-0.115.9-py3-none-any.whl.metadata (27 kB)\n", "Collecting uvicorn>=0.18.3 (from uvicorn[standard]>=0.18.3->chromadb)\n", "  Downloading uvicorn-0.34.3-py3-none-any.whl.metadata (6.5 kB)\n", "Collecting posthog>=2.4.0 (from chromadb)\n", "  Downloading posthog-4.10.0-py3-none-any.whl.metadata (6.0 kB)\n", "Requirement already satisfied: typing-extensions>=4.5.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from chromadb) (4.13.2)\n", "Collecting onnxruntime>=1.14.1 (from chromadb)\n", "  Downloading onnxruntime-1.22.0-cp313-cp313-macosx_13_0_universal2.whl.metadata (4.5 kB)\n", "Collecting opentelemetry-api>=1.2.0 (from chromadb)\n", "  Downloading opentelemetry_api-1.34.1-py3-none-any.whl.metadata (1.5 kB)\n", "Collecting opentelemetry-exporter-otlp-proto-grpc>=1.2.0 (from chromadb)\n", "  Downloading opentelemetry_exporter_otlp_proto_grpc-1.34.1-py3-none-any.whl.metadata (2.4 kB)\n", "Collecting opentelemetry-instrumentation-fastapi>=0.41b0 (from chromadb)\n", "  Downloading opentelemetry_instrumentation_fastapi-0.55b1-py3-none-any.whl.metadata (2.2 kB)\n", "Collecting opentelemetry-sdk>=1.2.0 (from chromadb)\n", "  Downloading opentelemetry_sdk-1.34.1-py3-none-any.whl.metadata (1.6 kB)\n", "Collecting tokenizers>=0.13.2 (from chromadb)\n", "  Downloading tokenizers-0.21.1-cp39-abi3-macosx_11_0_arm64.whl.metadata (6.8 kB)\n", "Collecting pypika>=0.48.9 (from chromadb)\n", "  Downloading PyPika-0.48.9.tar.gz (67 kB)\n", "  Installing build dependencies ... \u001b[?25ldone\n", "\u001b[?25h  Getting requirements to build wheel ... \u001b[?25ldone\n", "\u001b[?25h  Preparing metadata (pyproject.toml) ... \u001b[?25ldone\n", "\u001b[?25hRequirement already satisfied: tqdm>=4.65.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from chromadb) (4.67.1)\n", "Requirement already satisfied: overrides>=7.3.1 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from chromadb) (7.7.0)\n", "Requirement already satisfied: importlib-resources in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from chromadb) (6.5.2)\n", "Collecting grpcio>=1.58.0 (from chromadb)\n", "  Downloading grpcio-1.73.0-cp313-cp313-macosx_11_0_universal2.whl.metadata (3.8 kB)\n", "Collecting bcrypt>=4.0.1 (from chromadb)\n", "  Downloading bcrypt-4.3.0-cp39-abi3-macosx_10_12_universal2.whl.metadata (10 kB)\n", "Collecting typer>=0.9.0 (from chromadb)\n", "  Downloading typer-0.16.0-py3-none-any.whl.metadata (15 kB)\n", "Collecting kubernetes>=28.1.0 (from chromadb)\n", "  Downloading kubernetes-33.1.0-py2.py3-none-any.whl.metadata (1.7 kB)\n", "Requirement already satisfied: tenacity>=8.2.3 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from chromadb) (9.1.2)\n", "Requirement already satisfied: pyyaml>=6.0.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from chromadb) (6.0.2)\n", "Collecting mmh3>=4.0.1 (from chromadb)\n", "  Downloading mmh3-5.1.0-cp313-cp313-macosx_11_0_arm64.whl.metadata (16 kB)\n", "Requirement already satisfied: orjson>=3.9.12 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from chromadb) (3.10.16)\n", "Requirement already satisfied: httpx>=0.27.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from chromadb) (0.28.1)\n", "Collecting rich>=10.11.0 (from chromadb)\n", "  Downloading rich-14.0.0-py3-none-any.whl.metadata (18 kB)\n", "Requirement already satisfied: jsonschema>=4.19.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from chromadb) (4.24.0)\n", "Collecting starlette<0.46.0,>=0.40.0 (from fastapi==0.115.9->chromadb)\n", "  Downloading starlette-0.45.3-py3-none-any.whl.metadata (6.3 kB)\n", "Requirement already satisfied: packaging>=19.1 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from build>=1.0.3->chromadb) (24.2)\n", "Collecting pyproject_hooks (from build>=1.0.3->chromadb)\n", "  Downloading pyproject_hooks-1.2.0-py3-none-any.whl.metadata (1.3 kB)\n", "Requirement already satisfied: anyio in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from httpx>=0.27.0->chromadb) (4.9.0)\n", "Requirement already satisfied: certifi in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from httpx>=0.27.0->chromadb) (2025.4.26)\n", "Requirement already satisfied: httpcore==1.* in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from httpx>=0.27.0->chromadb) (1.0.9)\n", "Requirement already satisfied: idna in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from httpx>=0.27.0->chromadb) (3.10)\n", "Requirement already satisfied: h11>=0.16 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from httpcore==1.*->httpx>=0.27.0->chromadb) (0.16.0)\n", "Requirement already satisfied: attrs>=22.2.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from jsonschema>=4.19.0->chromadb) (25.3.0)\n", "Requirement already satisfied: jsonschema-specifications>=2023.03.6 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from jsonschema>=4.19.0->chromadb) (2025.4.1)\n", "Requirement already satisfied: referencing>=0.28.4 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from jsonschema>=4.19.0->chromadb) (0.36.2)\n", "Requirement already satisfied: rpds-py>=0.7.1 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from jsonschema>=4.19.0->chromadb) (0.25.1)\n", "Requirement already satisfied: six>=1.9.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from kubernetes>=28.1.0->chromadb) (1.17.0)\n", "Requirement already satisfied: python-dateutil>=2.5.3 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from kubernetes>=28.1.0->chromadb) (2.9.0.post0)\n", "Collecting google-auth>=1.0.1 (from kubernetes>=28.1.0->chromadb)\n", "  Downloading google_auth-2.40.3-py2.py3-none-any.whl.metadata (6.2 kB)\n", "Requirement already satisfied: websocket-client!=0.40.0,!=0.41.*,!=0.42.*,>=0.32.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from kubernetes>=28.1.0->chromadb) (1.8.0)\n", "Requirement already satisfied: requests in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from kubernetes>=28.1.0->chromadb) (2.32.3)\n", "Collecting requests-oauthlib (from kubernetes>=28.1.0->chromadb)\n", "  Downloading requests_oauthlib-2.0.0-py2.py3-none-any.whl.metadata (11 kB)\n", "Collecting oauthlib>=3.2.2 (from kubernetes>=28.1.0->chromadb)\n", "  Downloading oauthlib-3.2.2-py3-none-any.whl.metadata (7.5 kB)\n", "Requirement already satisfied: urllib3>=1.24.2 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from kubernetes>=28.1.0->chromadb) (2.4.0)\n", "Collecting durationpy>=0.7 (from kubernetes>=28.1.0->chromadb)\n", "  Downloading durationpy-0.10-py3-none-any.whl.metadata (340 bytes)\n", "Collecting langsmith<0.4,>=0.3.45 (from langchain-core>=0.3.60->langchain-chroma)\n", "  Downloading langsmith-0.3.45-py3-none-any.whl.metadata (15 kB)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langchain-core>=0.3.60->langchain-chroma) (1.33)\n", "Collecting coloredlogs (from onnxruntime>=1.14.1->chromadb)\n", "  Downloading coloredlogs-15.0.1-py2.py3-none-any.whl.metadata (12 kB)\n", "Collecting flatbuffers (from onnxruntime>=1.14.1->chromadb)\n", "  Downloading flatbuffers-25.2.10-py2.py3-none-any.whl.metadata (875 bytes)\n", "Collecting protobuf (from onnxruntime>=1.14.1->chromadb)\n", "  Downloading protobuf-6.31.1-cp39-abi3-macosx_10_9_universal2.whl.metadata (593 bytes)\n", "Collecting sympy (from onnxruntime>=1.14.1->chromadb)\n", "  Downloading sympy-1.14.0-py3-none-any.whl.metadata (12 kB)\n", "Requirement already satisfied: importlib-metadata<8.8.0,>=6.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from opentelemetry-api>=1.2.0->chromadb) (8.6.1)\n", "Collecting googleapis-common-protos~=1.52 (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb)\n", "  Downloading googleapis_common_protos-1.70.0-py3-none-any.whl.metadata (9.3 kB)\n", "Collecting opentelemetry-exporter-otlp-proto-common==1.34.1 (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb)\n", "  Downloading opentelemetry_exporter_otlp_proto_common-1.34.1-py3-none-any.whl.metadata (1.9 kB)\n", "Collecting opentelemetry-proto==1.34.1 (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb)\n", "  Downloading opentelemetry_proto-1.34.1-py3-none-any.whl.metadata (2.4 kB)\n", "Collecting protobuf (from onnxruntime>=1.14.1->chromadb)\n", "  Downloading protobuf-5.29.5-cp38-abi3-macosx_10_9_universal2.whl.metadata (592 bytes)\n", "Collecting opentelemetry-instrumentation-asgi==0.55b1 (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb)\n", "  Downloading opentelemetry_instrumentation_asgi-0.55b1-py3-none-any.whl.metadata (2.0 kB)\n", "Collecting opentelemetry-instrumentation==0.55b1 (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb)\n", "  Downloading opentelemetry_instrumentation-0.55b1-py3-none-any.whl.metadata (6.7 kB)\n", "Collecting opentelemetry-semantic-conventions==0.55b1 (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb)\n", "  Downloading opentelemetry_semantic_conventions-0.55b1-py3-none-any.whl.metadata (2.5 kB)\n", "Collecting opentelemetry-util-http==0.55b1 (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb)\n", "  Downloading opentelemetry_util_http-0.55b1-py3-none-any.whl.metadata (2.6 kB)\n", "Requirement already satisfied: wrapt<2.0.0,>=1.0.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from opentelemetry-instrumentation==0.55b1->opentelemetry-instrumentation-fastapi>=0.41b0->chromadb) (1.17.2)\n", "Collecting asgiref~=3.0 (from opentelemetry-instrumentation-asgi==0.55b1->opentelemetry-instrumentation-fastapi>=0.41b0->chromadb)\n", "  Downloading asgiref-3.8.1-py3-none-any.whl.metadata (9.3 kB)\n", "Requirement already satisfied: backoff>=1.10.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from posthog>=2.4.0->chromadb) (2.2.1)\n", "Requirement already satisfied: distro>=1.5.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from posthog>=2.4.0->chromadb) (1.9.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from pydantic>=1.9->chromadb) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.1 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from pydantic>=1.9->chromadb) (2.33.1)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from pydantic>=1.9->chromadb) (0.4.0)\n", "Collecting markdown-it-py>=2.2.0 (from rich>=10.11.0->chromadb)\n", "  Downloading markdown_it_py-3.0.0-py3-none-any.whl.metadata (6.9 kB)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from rich>=10.11.0->chromadb) (2.19.1)\n", "Collecting huggingface-hub<1.0,>=0.16.4 (from tokenizers>=0.13.2->chromadb)\n", "  Downloading huggingface_hub-0.33.0-py3-none-any.whl.metadata (14 kB)\n", "Requirement already satisfied: click>=8.0.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from typer>=0.9.0->chromadb) (8.2.1)\n", "Collecting shellingham>=1.3.0 (from typer>=0.9.0->chromadb)\n", "  Downloading shellingham-1.5.4-py2.py3-none-any.whl.metadata (3.5 kB)\n", "Collecting httptools>=0.6.3 (from uvicorn[standard]>=0.18.3->chromadb)\n", "  Downloading httptools-0.6.4-cp313-cp313-macosx_11_0_arm64.whl.metadata (3.6 kB)\n", "Requirement already satisfied: python-dotenv>=0.13 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from uvicorn[standard]>=0.18.3->chromadb) (1.1.0)\n", "Collecting uvloop>=0.15.1 (from uvicorn[standard]>=0.18.3->chromadb)\n", "  Downloading uvloop-0.21.0-cp313-cp313-macosx_10_13_universal2.whl.metadata (4.9 kB)\n", "Collecting watchfiles>=0.13 (from uvicorn[standard]>=0.18.3->chromadb)\n", "  Downloading watchfiles-1.0.5-cp313-cp313-macosx_11_0_arm64.whl.metadata (4.9 kB)\n", "Collecting websockets>=10.4 (from uvicorn[standard]>=0.18.3->chromadb)\n", "  Downloading websockets-15.0.1-cp313-cp313-macosx_11_0_arm64.whl.metadata (6.8 kB)\n", "Collecting cachetools<6.0,>=2.0.0 (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb)\n", "  Downloading cachetools-5.5.2-py3-none-any.whl.metadata (5.4 kB)\n", "Collecting pyasn1-modules>=0.2.1 (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb)\n", "  Downloading pyasn1_modules-0.4.2-py3-none-any.whl.metadata (3.5 kB)\n", "Collecting rsa<5,>=3.1.4 (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb)\n", "  Downloading rsa-4.9.1-py3-none-any.whl.metadata (5.6 kB)\n", "Collecting filelock (from huggingface-hub<1.0,>=0.16.4->tokenizers>=0.13.2->chromadb)\n", "  Downloading filelock-3.18.0-py3-none-any.whl.metadata (2.9 kB)\n", "Collecting fsspec>=2023.5.0 (from huggingface-hub<1.0,>=0.16.4->tokenizers>=0.13.2->chromadb)\n", "  Downloading fsspec-2025.5.1-py3-none-any.whl.metadata (11 kB)\n", "Collecting hf-xet<2.0.0,>=1.1.2 (from huggingface-hub<1.0,>=0.16.4->tokenizers>=0.13.2->chromadb)\n", "  Downloading hf_xet-1.1.3-cp37-abi3-macosx_11_0_arm64.whl.metadata (879 bytes)\n", "Requirement already satisfied: zipp>=3.20 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from importlib-metadata<8.8.0,>=6.0->opentelemetry-api>=1.2.0->chromadb) (3.21.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from jsonpatch<2.0,>=1.33->langchain-core>=0.3.60->langchain-chroma) (3.0.0)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langsmith<0.4,>=0.3.45->langchain-core>=0.3.60->langchain-chroma) (1.0.0)\n", "Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langsmith<0.4,>=0.3.45->langchain-core>=0.3.60->langchain-chroma) (0.23.0)\n", "Collecting mdurl~=0.1 (from markdown-it-py>=2.2.0->rich>=10.11.0->chromadb)\n", "  Downloading mdurl-0.1.2-py3-none-any.whl.metadata (1.6 kB)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from requests->kubernetes>=28.1.0->chromadb) (3.4.1)\n", "Requirement already satisfied: sniffio>=1.1 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from anyio->httpx>=0.27.0->chromadb) (1.3.1)\n", "Collecting humanfriendly>=9.1 (from coloredlogs->onnxruntime>=1.14.1->chromadb)\n", "  Downloading humanfriendly-10.0-py2.py3-none-any.whl.metadata (9.2 kB)\n", "Collecting mpmath<1.4,>=1.1.0 (from sympy->onnxruntime>=1.14.1->chromadb)\n", "  Downloading mpmath-1.3.0-py3-none-any.whl.metadata (8.6 kB)\n", "Collecting pyasn1<0.7.0,>=0.6.1 (from pyasn1-modules>=0.2.1->google-auth>=1.0.1->kubernetes>=28.1.0->chromadb)\n", "  Downloading pyasn1-0.6.1-py3-none-any.whl.metadata (8.4 kB)\n", "Downloading langchain_chroma-0.2.4-py3-none-any.whl (11 kB)\n", "Downloading chromadb-1.0.12-cp39-abi3-macosx_11_0_arm64.whl (17.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m17.9/17.9 MB\u001b[0m \u001b[31m44.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hDownloading fastapi-0.115.9-py3-none-any.whl (94 kB)\n", "Downloading bcrypt-4.3.0-cp39-abi3-macosx_10_12_universal2.whl (498 kB)\n", "Downloading build-1.2.2.post1-py3-none-any.whl (22 kB)\n", "Downloading grpcio-1.73.0-cp313-cp313-macosx_11_0_universal2.whl (10.6 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m10.6/10.6 MB\u001b[0m \u001b[31m51.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m\n", "\u001b[?25hDownloading kubernetes-33.1.0-py2.py3-none-any.whl (1.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.9/1.9 MB\u001b[0m \u001b[31m40.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading langchain_core-0.3.65-py3-none-any.whl (438 kB)\n", "Downloading mmh3-5.1.0-cp313-cp313-macosx_11_0_arm64.whl (40 kB)\n", "Downloading onnxruntime-1.22.0-cp313-cp313-macosx_13_0_universal2.whl (34.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m34.3/34.3 MB\u001b[0m \u001b[31m45.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hDownloading opentelemetry_api-1.34.1-py3-none-any.whl (65 kB)\n", "Downloading opentelemetry_exporter_otlp_proto_grpc-1.34.1-py3-none-any.whl (18 kB)\n", "Downloading opentelemetry_exporter_otlp_proto_common-1.34.1-py3-none-any.whl (18 kB)\n", "Downloading opentelemetry_proto-1.34.1-py3-none-any.whl (55 kB)\n", "Downloading opentelemetry_instrumentation_fastapi-0.55b1-py3-none-any.whl (12 kB)\n", "Downloading opentelemetry_instrumentation-0.55b1-py3-none-any.whl (31 kB)\n", "Downloading opentelemetry_instrumentation_asgi-0.55b1-py3-none-any.whl (16 kB)\n", "Downloading opentelemetry_semantic_conventions-0.55b1-py3-none-any.whl (196 kB)\n", "Downloading opentelemetry_util_http-0.55b1-py3-none-any.whl (7.3 kB)\n", "Downloading opentelemetry_sdk-1.34.1-py3-none-any.whl (118 kB)\n", "Downloading posthog-4.10.0-py3-none-any.whl (102 kB)\n", "Downloading rich-14.0.0-py3-none-any.whl (243 kB)\n", "Downloading tokenizers-0.21.1-cp39-abi3-macosx_11_0_arm64.whl (2.7 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.7/2.7 MB\u001b[0m \u001b[31m38.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading typer-0.16.0-py3-none-any.whl (46 kB)\n", "Downloading uvicorn-0.34.3-py3-none-any.whl (62 kB)\n", "Downloading durationpy-0.10-py3-none-any.whl (3.9 kB)\n", "Downloading google_auth-2.40.3-py2.py3-none-any.whl (216 kB)\n", "Downloading googleapis_common_protos-1.70.0-py3-none-any.whl (294 kB)\n", "Downloading httptools-0.6.4-cp313-cp313-macosx_11_0_arm64.whl (102 kB)\n", "Downloading huggingface_hub-0.33.0-py3-none-any.whl (514 kB)\n", "Downloading langsmith-0.3.45-py3-none-any.whl (363 kB)\n", "Downloading markdown_it_py-3.0.0-py3-none-any.whl (87 kB)\n", "Downloading oauthlib-3.2.2-py3-none-any.whl (151 kB)\n", "Downloading protobuf-5.29.5-cp38-abi3-macosx_10_9_universal2.whl (418 kB)\n", "Downloading shellingham-1.5.4-py2.py3-none-any.whl (9.8 kB)\n", "Downloading starlette-0.45.3-py3-none-any.whl (71 kB)\n", "Downloading uvloop-0.21.0-cp313-cp313-macosx_10_13_universal2.whl (1.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.5/1.5 MB\u001b[0m \u001b[31m42.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading watchfiles-1.0.5-cp313-cp313-macosx_11_0_arm64.whl (392 kB)\n", "Downloading websockets-15.0.1-cp313-cp313-macosx_11_0_arm64.whl (173 kB)\n", "Downloading coloredlogs-15.0.1-py2.py3-none-any.whl (46 kB)\n", "Downloading flatbuffers-25.2.10-py2.py3-none-any.whl (30 kB)\n", "Downloading pyproject_hooks-1.2.0-py3-none-any.whl (10 kB)\n", "Downloading requests_oauthlib-2.0.0-py2.py3-none-any.whl (24 kB)\n", "Downloading sympy-1.14.0-py3-none-any.whl (6.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m6.3/6.3 MB\u001b[0m \u001b[31m34.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading asgiref-3.8.1-py3-none-any.whl (23 kB)\n", "Downloading cachetools-5.5.2-py3-none-any.whl (10 kB)\n", "Downloading fsspec-2025.5.1-py3-none-any.whl (199 kB)\n", "Downloading hf_xet-1.1.3-cp37-abi3-macosx_11_0_arm64.whl (2.2 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.2/2.2 MB\u001b[0m \u001b[31m46.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading humanfriendly-10.0-py2.py3-none-any.whl (86 kB)\n", "Downloading mdurl-0.1.2-py3-none-any.whl (10.0 kB)\n", "Downloading mpmath-1.3.0-py3-none-any.whl (536 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m536.2/536.2 kB\u001b[0m \u001b[31m16.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading pyasn1_modules-0.4.2-py3-none-any.whl (181 kB)\n", "Downloading rsa-4.9.1-py3-none-any.whl (34 kB)\n", "Downloading filelock-3.18.0-py3-none-any.whl (16 kB)\n", "Downloading pyasn1-0.6.1-py3-none-any.whl (83 kB)\n", "Building wheels for collected packages: pypika\n", "  Building wheel for pypika (pyproject.toml) ... \u001b[?25ldone\n", "\u001b[?25h  Created wheel for pypika: filename=pypika-0.48.9-py2.py3-none-any.whl size=53803 sha256=e31c273224cb98546fccc40f1d4327ddbf866ee596df81e347729cd7c5be2931\n", "  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/b4/f8/a5/28e9c1524d320f4b8eefdce0e487b5c2e128dbf2ed1bb4a60b\n", "Successfully built pypika\n", "Installing collected packages: pypika, mpmath, flatbuffers, durationpy, websockets, uvloop, uvicorn, sympy, shellingham, pyproject_hooks, pyasn1, protobuf, opentelemetry-util-http, oauthlib, mmh3, mdurl, humanfriendly, httptools, hf-xet, grpcio, fsspec, filelock, cachetools, bcrypt, asgiref, watchfiles, starlette, rsa, requests-oauthlib, pyasn1-modules, posthog, opentelemetry-proto, opentelemetry-api, markdown-it-py, huggingface-hub, googleapis-common-protos, coloredlogs, build, tokenizers, rich, opentelemetry-semantic-conventions, opentelemetry-exporter-otlp-proto-common, onnxruntime, langsmith, google-auth, fastapi, typer, opentelemetry-sdk, opentelemetry-instrumentation, langchain-core, kubernetes, opentelemetry-instrumentation-asgi, opentelemetry-exporter-otlp-proto-grpc, opentelemetry-instrumentation-fastapi, chromadb, langchain-chroma\n", "  Attempting uninstall: langsmith\n", "    Found existing installation: langsmith 0.2.11\n", "    Uninstalling langsmith-0.2.11:\n", "      Successfully uninstalled langsmith-0.2.11\n", "  Attempting uninstall: langchain-core\n", "    Found existing installation: langchain-core 0.3.56\n", "    Uninstalling langchain-core-0.3.56:\n", "      Successfully uninstalled langchain-core-0.3.56\n", "Successfully installed asgiref-3.8.1 bcrypt-4.3.0 build-1.2.2.post1 cachetools-5.5.2 chromadb-1.0.12 coloredlogs-15.0.1 durationpy-0.10 fastapi-0.115.9 filelock-3.18.0 flatbuffers-25.2.10 fsspec-2025.5.1 google-auth-2.40.3 googleapis-common-protos-1.70.0 grpcio-1.73.0 hf-xet-1.1.3 httptools-0.6.4 huggingface-hub-0.33.0 humanfriendly-10.0 kubernetes-33.1.0 langchain-chroma-0.2.4 langchain-core-0.3.65 langsmith-0.3.45 markdown-it-py-3.0.0 mdurl-0.1.2 mmh3-5.1.0 mpmath-1.3.0 oauthlib-3.2.2 onnxruntime-1.22.0 opentelemetry-api-1.34.1 opentelemetry-exporter-otlp-proto-common-1.34.1 opentelemetry-exporter-otlp-proto-grpc-1.34.1 opentelemetry-instrumentation-0.55b1 opentelemetry-instrumentation-asgi-0.55b1 opentelemetry-instrumentation-fastapi-0.55b1 opentelemetry-proto-1.34.1 opentelemetry-sdk-1.34.1 opentelemetry-semantic-conventions-0.55b1 opentelemetry-util-http-0.55b1 posthog-4.10.0 protobuf-5.29.5 pyasn1-0.6.1 pyasn1-modules-0.4.2 pypika-0.48.9 pyproject_hooks-1.2.0 requests-oauthlib-2.0.0 rich-14.0.0 rsa-4.9.1 shellingham-1.5.4 starlette-0.45.3 sympy-1.14.0 tokenizers-0.21.1 typer-0.16.0 uvicorn-0.34.3 uvloop-0.21.0 watchfiles-1.0.5 websockets-15.0.1\n"]}], "source": ["! pip install langchain-chroma chromadb"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Created a chunk of size 181, which is longer than the specified 150\n"]}, {"data": {"text/plain": ["[Document(id='bc4f8377-6310-433e-be51-ed0143d47379', metadata={'source': 'test.docx'}, page_content='{\\n\\n                \"enName\": \"zwfwCatalogCode\",\\n\\n                \"zhName\": \"基本编码\",\\n\\n                \"type\": \"测试字典类型11\",'),\n", " Document(id='6dd4d176-8daa-4550-bec9-885ea9dee8c4', metadata={'source': 'test.docx'}, page_content='{\\n\\n                \"enName\": \"zwfwCatalogCode\",\\n\\n                \"zhName\": \"基本编码\",\\n\\n                \"type\": \"测试字典类型11\",'),\n", " Document(id='30c8cacb-2c19-4c07-a60a-4990191ff503', metadata={'source': 'test.docx'}, page_content='{\\n\\n                \"enName\": \"zwfwCatalogCode\",\\n\\n                \"zhName\": \"基本编码\",\\n\\n                \"type\": \"测试字典类型11\",'),\n", " Document(id='783fe19b-de74-4b55-9f53-c29b4f089c9d', metadata={'source': 'test.docx'}, page_content='{\\n\\n                \"enName\": \"zwfwCatalogCode\",\\n\\n                \"zhName\": \"基本编码\",\\n\\n                \"type\": \"测试字典类型11\",')]"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["#导入必须的包\n", "# Import therequired packages\n", "from langchain_community.document_loaders import UnstructuredExcelLoader,Docx2txtLoader, PyPDFLoader\n", "from langchain_text_splitters import CharacterTextSplitter \n", "from langchain_chroma import Chroma \n", "from langchain_openai import OpenAIEmbeddings \n", "import os\n", "\n", "# 使用国产模型\n", "embeddings_model = OpenAIEmbeddings(model=\"BAAI/bge-m3\",api_key=\"sk-vgbdlqjvfytxctphzbbdzcfzzqhpmrjhjynftgnpdkchbjfd\",base_url=\"https://api.siliconflow.cn/v1\")\n", "\n", "# 定义chatdoc\n", "\n", "class ChatDoc():\n", "    def __init__(self):\n", "        self.doc = None\n", "        self.splitText = []\n", "        self.file_path = None\n", "\n", "    def getFile(self):\n", "        doc = self.doc\n", "        loaders = {\n", "            \"docx\":Docx2txtLoader,\n", "            \"pdf\":<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "            \"xlsx\":UnstructuredExcelLoader,\n", "        }\n", "        file_extension = doc.split(\".\")[-1]\n", "        loader_class = loaders.get(file_extension)\n", "        if loader_class:\n", "            try:\n", "                loader = loader_class(doc)\n", "                text = loader.load()\n", "                return text\n", "            except Exception as e:\n", "                print(f\"Error loading {file_extension} files:{e}\")\n", "        else:\n", "            print(f\"Unsupported file extension: {file_extension}\")\n", "            return None\n", "    # 分割文本\n", "    def splitSentence(self):\n", "        full_text = self.getFile()\n", "        if full_text != None:\n", "            text_splitter = CharacterTextSplitter(chunk_size=150, chunk_overlap=20)\n", "            texts = text_splitter.split_documents(full_text)\n", "            self.splitText = texts\n", "        else:\n", "            print(\"文件加载失败\")\n", "\n", "    def getSplitText(self):\n", "        return self.splitText\n", "    #向量化向量存贮\n", "    def embeddingAmdVectorDB(self):\n", "        db = Chroma.from_documents(self.splitText,embeddings_model)\n", "        return db\n", "    # 向量化\n", "    def embedding(self):\n", "        return embeddings_model.embed_documents(self.splitText)\n", "    # 向量检索\n", "    def vectorSearch(self,query,k=5):\n", "        db = self.embeddingAmdVectorDB()\n", "        return db.similarity_search(query,k)\n", "    #提问并找到相关文本快，使用简单的检索\n", "    def askANdFindFiles(self,query):\n", "        db = self.embeddingAmdVectorDB()\n", "        retriever = db.as_retriever()\n", "        results = retriever.invoke(query)\n", "        return results\n", "\n", "chat_doc = ChatDoc()\n", "chat_doc.doc = \"test.docx\"\n", "chat_doc.splitSentence()\n", "chat_doc.askANdFindFiles(\"版本号是多少？\")\n", "\n", "\n", "# 测试ChatDoc类\n", "# Test ChatDoc class"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting langchain-deepseek\n", "  Downloading langchain_deepseek-0.1.3-py3-none-any.whl.metadata (1.1 kB)\n", "Requirement already satisfied: langchain-core<1.0.0,>=0.3.47 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langchain-deepseek) (0.3.65)\n", "Requirement already satisfied: langchain-openai<1.0.0,>=0.3.9 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langchain-deepseek) (0.3.14)\n", "Requirement already satisfied: langsmith<0.4,>=0.3.45 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (0.3.45)\n", "Requirement already satisfied: tenacity!=8.4.0,<10.0.0,>=8.1.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (9.1.2)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (1.33)\n", "Requirement already satisfied: PyYAML>=5.3 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (6.0.2)\n", "Requirement already satisfied: packaging<25,>=23.2 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (24.2)\n", "Requirement already satisfied: typing-extensions>=4.7 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (4.13.2)\n", "Requirement already satisfied: pydantic>=2.7.4 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (2.11.3)\n", "Requirement already satisfied: openai<2.0.0,>=1.68.2 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langchain-openai<1.0.0,>=0.3.9->langchain-deepseek) (1.84.0)\n", "Requirement already satisfied: tiktoken<1,>=0.7 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langchain-openai<1.0.0,>=0.3.9->langchain-deepseek) (0.9.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (3.0.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langsmith<0.4,>=0.3.45->langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (0.28.1)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langsmith<0.4,>=0.3.45->langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (3.10.16)\n", "Requirement already satisfied: requests<3,>=2 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langsmith<0.4,>=0.3.45->langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (2.32.3)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langsmith<0.4,>=0.3.45->langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (1.0.0)\n", "Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langsmith<0.4,>=0.3.45->langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (0.23.0)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from openai<2.0.0,>=1.68.2->langchain-openai<1.0.0,>=0.3.9->langchain-deepseek) (4.9.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from openai<2.0.0,>=1.68.2->langchain-openai<1.0.0,>=0.3.9->langchain-deepseek) (1.9.0)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from openai<2.0.0,>=1.68.2->langchain-openai<1.0.0,>=0.3.9->langchain-deepseek) (0.9.0)\n", "Requirement already satisfied: sniffio in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from openai<2.0.0,>=1.68.2->langchain-openai<1.0.0,>=0.3.9->langchain-deepseek) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from openai<2.0.0,>=1.68.2->langchain-openai<1.0.0,>=0.3.9->langchain-deepseek) (4.67.1)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from pydantic>=2.7.4->langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.1 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from pydantic>=2.7.4->langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (2.33.1)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from pydantic>=2.7.4->langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (0.4.0)\n", "Requirement already satisfied: regex>=2022.1.18 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from tiktoken<1,>=0.7->langchain-openai<1.0.0,>=0.3.9->langchain-deepseek) (2024.11.6)\n", "Requirement already satisfied: idna>=2.8 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from anyio<5,>=3.5.0->openai<2.0.0,>=1.68.2->langchain-openai<1.0.0,>=0.3.9->langchain-deepseek) (3.10)\n", "Requirement already satisfied: certifi in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.3.45->langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (2025.4.26)\n", "Requirement already satisfied: httpcore==1.* in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.3.45->langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.4,>=0.3.45->langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (0.16.0)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from requests<3,>=2->langsmith<0.4,>=0.3.45->langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (3.4.1)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from requests<3,>=2->langsmith<0.4,>=0.3.45->langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (2.4.0)\n", "Downloading langchain_deepseek-0.1.3-py3-none-any.whl (7.1 kB)\n", "Installing collected packages: langchain-deepseek\n", "Successfully installed langchain-deepseek-0.1.3\n"]}], "source": ["! pip install langchain-deepseek"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 开始处理文档 ===\n", "成功加载 xlsx 文件，共 1 个文档片段\n", "文本分割完成，共 1 个片段\n", "片段 1: 发起时间 完成时间 发起人姓名 发起人部门 请假类型 开始时间 结束时间 时长 2024-12-31 11:16:45 2024-12-31 12:07:56 王小兮 研发三部 年假 2025-01-...\n", "\n", "=== 测试不同的查询 ===\n", "1. 测试通用查询：\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.siliconflow.cn/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.siliconflow.cn/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.siliconflow.cn/v1/embeddings \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["向量数据库创建成功\n", "基础检索找到 5 个相关片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.deepseek.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.deepseek.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.deepseek.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.deepseek.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["压缩检索找到 0 个相关片段\n", "结果数量: 0\n", "\n", "2. 测试原始查询：\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.siliconflow.cn/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.siliconflow.cn/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.siliconflow.cn/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.deepseek.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["向量数据库创建成功\n", "基础检索找到 5 个相关片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.deepseek.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.deepseek.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.deepseek.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["压缩检索找到 0 个相关片段\n", "结果数量: 0\n", "没有找到相关数据\n"]}], "source": ["# 查询重写提高文档检索准确性\n", "\n", "#导入必须的包\n", "# Import therequired packages\n", "from langchain_community.document_loaders import UnstructuredExcelLoader,Docx2txtLoader, PyPDFLoader\n", "from langchain_text_splitters import CharacterTextSplitter \n", "from langchain_chroma import Chroma \n", "from langchain_openai import OpenAIEmbeddings \n", "from langchain.retrievers.multi_query import MultiQueryRetriever\n", "import os\n", "from langchain_deepseek import ChatDeepSeek\n", "from langchain.retrievers import ContextualCompressionRetriever\n", "from langchain.retrievers.document_compressors import LLMChainExtractor\n", "\n", "# 使用国产模型\n", "embeddings_model = OpenAIEmbeddings(model=\"BAAI/bge-m3\",api_key=\"sk-vgbdlqjvfytxctphzbbdzcfzzqhpmrjhjynftgnpdkchbjfd\",base_url=\"https://api.siliconflow.cn/v1\")\n", "\n", "llm = ChatDeepSeek(\n", "    model=\"deepseek-chat\",\n", "    temperature=0,\n", "    api_key=\"sk-778deaff83d44d3a864caa8f3cf46c75\",\n", "    base_url=\"https://api.deepseek.com/v1\"\n", ")\n", "# 定义chatdoc\n", "\n", "class ChatDoc():\n", "    def __init__(self):\n", "        self.doc = None\n", "        self.splitText = []\n", "        self.file_path = None\n", "\n", "    def getFile(self):\n", "        doc = self.doc\n", "        if not doc:\n", "            print(\"错误：未设置文档路径\")\n", "            return None\n", "            \n", "        loaders = {\n", "            \"docx\":Docx2txtLoader,\n", "            \"pdf\":<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "            \"xlsx\":UnstructuredExcelLoader,\n", "        }\n", "        file_extension = doc.split(\".\")[-1]\n", "        loader_class = loaders.get(file_extension)\n", "        if loader_class:\n", "            try:\n", "                loader = loader_class(doc)\n", "                text = loader.load()\n", "                print(f\"成功加载 {file_extension} 文件，共 {len(text)} 个文档片段\")\n", "                return text\n", "            except Exception as e:\n", "                print(f\"Error loading {file_extension} files:{e}\")\n", "                return None\n", "        else:\n", "            print(f\"Unsupported file extension: {file_extension}\")\n", "            return None\n", "    \n", "    # 分割文本\n", "    def splitSentence(self):\n", "        full_text = self.getFile()\n", "        if full_text != None:\n", "            text_splitter = CharacterTextSplitter(chunk_size=150, chunk_overlap=20)\n", "            texts = text_splitter.split_documents(full_text)\n", "            self.splitText = texts\n", "            print(f\"文本分割完成，共 {len(texts)} 个片段\")\n", "            # 打印前几个片段的内容用于调试\n", "            for i, text in enumerate(texts[:3]):\n", "                print(f\"片段 {i+1}: {text.page_content[:100]}...\")\n", "        else:\n", "            print(\"文件加载失败\")\n", "\n", "    def getSplitText(self):\n", "        return self.splitText\n", "    \n", "    #向量化向量存贮\n", "    def embeddingAmdVectorDB(self):\n", "        if not self.splitText:\n", "            print(\"错误：没有分割的文本数据\")\n", "            return None\n", "        try:\n", "            db = Chroma.from_documents(self.splitText,embeddings_model)\n", "            print(\"向量数据库创建成功\")\n", "            return db\n", "        except Exception as e:\n", "            print(f\"向量化失败: {e}\")\n", "            return None\n", "    \n", "    # 向量化\n", "    def embedding(self):\n", "        return embeddings_model.embed_documents(self.splitText)\n", "    \n", "    # 向量检索\n", "    def vectorSearch(self,query,k=5):\n", "        db = self.embeddingAmdVectorDB()\n", "        if db:\n", "            results = db.similarity_search(query,k)\n", "            print(f\"基础向量检索找到 {len(results)} 个相关片段\")\n", "            return results\n", "        return []\n", "    \n", "    #提问并找到相关文本快，使用压缩检索\n", "    def askANdFindFiles(self,query):\n", "        db = self.embeddingAmdVectorDB()\n", "        if not db:\n", "            print(\"向量数据库创建失败\")\n", "            return []\n", "            \n", "        try:\n", "            # 先尝试基础检索看看是否有结果\n", "            basic_results = db.similarity_search(query, k=5)\n", "            print(f\"基础检索找到 {len(basic_results)} 个相关片段\")\n", "            \n", "            if not basic_results:\n", "                print(\"基础检索没有找到相关内容，请检查查询词是否存在于文档中\")\n", "                return []\n", "            \n", "            # 创建上下文压缩检索 - 修复参数名\n", "            retriever = db.as_retriever()\n", "            compressor = LLMChainExtractor.from_llm(llm=llm)\n", "            compressed_retriever = ContextualCompressionRetriever(\n", "                base_retriever=retriever,\n", "                base_compressor=compressor,  # 修复：使用正确的参数名\n", "            )\n", "            results = compressed_retriever.invoke(query)\n", "            print(f\"压缩检索找到 {len(results)} 个相关片段\")\n", "            return results\n", "            \n", "        except Exception as e:\n", "            print(f\"检索过程出错: {e}\")\n", "            # 如果压缩检索失败，返回基础检索结果\n", "            return self.vectorSearch(query)\n", "\n", "# 测试修复后的代码\n", "chat_doc = ChatDoc()\n", "chat_doc.doc = \"test.xlsx\"\n", "print(\"=== 开始处理文档 ===\")\n", "chat_doc.splitSentence()\n", "\n", "print(\"\\n=== 测试不同的查询 ===\")\n", "\n", "# 先测试一个更通用的查询\n", "print(\"1. 测试通用查询：\")\n", "unique_doc = chat_doc.askANdFindFiles(\"请假\")\n", "print(f\"结果数量: {len(unique_doc)}\")\n", "for i, doc in enumerate(unique_doc[:2]):\n", "    print(f\"结果 {i+1}: {doc.page_content[:200]}...\")\n", "\n", "print(\"\\n2. 测试原始查询：\")\n", "unique_doc = chat_doc.askANdFindFiles(\"找到付东明的数据\")\n", "print(f\"结果数量: {len(unique_doc)}\")\n", "if unique_doc:\n", "    for i, doc in enumerate(unique_doc):\n", "        print(f\"结果 {i+1}: {doc.page_content[:200]}...\")\n", "else:\n", "    print(\"没有找到相关数据\")\n", "\n", "# 测试ChatDoc类\n", "# Test ChatDoc class"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 测试修复后的ChatDoc类 ===\n", "=== 开始处理文档 ===\n", "成功加载 xlsx 文件，共 1 个文档片段\n", "文本分割完成，共 1 个片段\n", "片段 1: 发起时间 完成时间 发起人姓名 发起人部门 请假类型 开始时间 结束时间 时长 2024-12-31 11:16:45 2024-12-31 12:07:56 王小兮 研发三部 年假 2025-01-...\n", "\n", "=== 测试不同的查询 ===\n", "1. 测试通用查询：\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.siliconflow.cn/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.siliconflow.cn/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.siliconflow.cn/v1/embeddings \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["向量数据库创建成功\n", "基础检索找到 5 个相关片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.deepseek.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.deepseek.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.deepseek.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.deepseek.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["压缩检索找到 0 个相关片段\n", "结果数量: 0\n", "\n", "2. 测试原始查询：\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.siliconflow.cn/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.siliconflow.cn/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.siliconflow.cn/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.deepseek.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["向量数据库创建成功\n", "基础检索找到 5 个相关片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.deepseek.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.deepseek.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.deepseek.com/v1/chat/completions \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["压缩检索找到 0 个相关片段\n", "结果数量: 0\n", "没有找到相关数据\n"]}], "source": ["# 测试修复后的ChatDoc类\n", "from langchain_community.document_loaders import UnstructuredExcelLoader,Docx2txtLoader, PyPDFLoader\n", "from langchain_text_splitters import CharacterTextSplitter \n", "from langchain_chroma import Chroma \n", "from langchain_openai import OpenAIEmbeddings \n", "from langchain.retrievers.multi_query import MultiQueryRetriever\n", "import os\n", "from langchain_deepseek import ChatDeepSeek\n", "from langchain.retrievers import ContextualCompressionRetriever\n", "from langchain.retrievers.document_compressors import LLMChainExtractor\n", "\n", "# 使用国产模型\n", "embeddings_model = OpenAIEmbeddings(model=\"BAAI/bge-m3\",api_key=\"sk-vgbdlqjvfytxctphzbbdzcfzzqhpmrjhjynftgnpdkchbjfd\",base_url=\"https://api.siliconflow.cn/v1\")\n", "\n", "llm = ChatDeepSeek(\n", "    model=\"deepseek-chat\",\n", "    temperature=0,\n", "    api_key=\"sk-778deaff83d44d3a864caa8f3cf46c75\",\n", "    base_url=\"https://api.deepseek.com/v1\"\n", ")\n", "\n", "# 定义修复后的ChatDoc类\n", "class ChatDoc():\n", "    def __init__(self):\n", "        self.doc = None\n", "        self.splitText = []\n", "        self.file_path = None\n", "\n", "    def getFile(self):\n", "        doc = self.doc\n", "        if not doc:\n", "            print(\"错误：未设置文档路径\")\n", "            return None\n", "            \n", "        loaders = {\n", "            \"docx\":Docx2txtLoader,\n", "            \"pdf\":<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "            \"xlsx\":UnstructuredExcelLoader,\n", "        }\n", "        file_extension = doc.split(\".\")[-1]\n", "        loader_class = loaders.get(file_extension)\n", "        if loader_class:\n", "            try:\n", "                loader = loader_class(doc)\n", "                text = loader.load()\n", "                print(f\"成功加载 {file_extension} 文件，共 {len(text)} 个文档片段\")\n", "                return text\n", "            except Exception as e:\n", "                print(f\"Error loading {file_extension} files:{e}\")\n", "                return None\n", "        else:\n", "            print(f\"Unsupported file extension: {file_extension}\")\n", "            return None\n", "    \n", "    # 分割文本\n", "    def splitSentence(self):\n", "        full_text = self.getFile()\n", "        if full_text != None:\n", "            text_splitter = CharacterTextSplitter(chunk_size=150, chunk_overlap=20)\n", "            texts = text_splitter.split_documents(full_text)\n", "            self.splitText = texts\n", "            print(f\"文本分割完成，共 {len(texts)} 个片段\")\n", "            # 打印前几个片段的内容用于调试\n", "            for i, text in enumerate(texts[:3]):\n", "                print(f\"片段 {i+1}: {text.page_content[:100]}...\")\n", "        else:\n", "            print(\"文件加载失败\")\n", "\n", "    def getSplitText(self):\n", "        return self.splitText\n", "    \n", "    #向量化向量存贮\n", "    def embeddingAmdVectorDB(self):\n", "        if not self.splitText:\n", "            print(\"错误：没有分割的文本数据\")\n", "            return None\n", "        try:\n", "            db = Chroma.from_documents(self.splitText,embeddings_model)\n", "            print(\"向量数据库创建成功\")\n", "            return db\n", "        except Exception as e:\n", "            print(f\"向量化失败: {e}\")\n", "            return None\n", "    \n", "    # 向量化\n", "    def embedding(self):\n", "        return embeddings_model.embed_documents(self.splitText)\n", "    \n", "    # 向量检索\n", "    def vectorSearch(self,query,k=5):\n", "        db = self.embeddingAmdVectorDB()\n", "        if db:\n", "            results = db.similarity_search(query,k)\n", "            print(f\"基础向量检索找到 {len(results)} 个相关片段\")\n", "            return results\n", "        return []\n", "    \n", "    #提问并找到相关文本快，使用压缩检索\n", "    def askANdFindFiles(self,query):\n", "        db = self.embeddingAmdVectorDB()\n", "        if not db:\n", "            print(\"向量数据库创建失败\")\n", "            return []\n", "            \n", "        try:\n", "            # 先尝试基础检索看看是否有结果\n", "            basic_results = db.similarity_search(query, k=5)\n", "            print(f\"基础检索找到 {len(basic_results)} 个相关片段\")\n", "            \n", "            if not basic_results:\n", "                print(\"基础检索没有找到相关内容，请检查查询词是否存在于文档中\")\n", "                return []\n", "            \n", "            # 创建上下文压缩检索 - 修复参数名\n", "            retriever = db.as_retriever()\n", "            compressor = LLMChainExtractor.from_llm(llm=llm)\n", "            compressed_retriever = ContextualCompressionRetriever(\n", "                base_retriever=retriever,\n", "                base_compressor=compressor,  # 修复：使用正确的参数名\n", "            )\n", "            results = compressed_retriever.invoke(query)\n", "            print(f\"压缩检索找到 {len(results)} 个相关片段\")\n", "            return results\n", "            \n", "        except Exception as e:\n", "            print(f\"检索过程出错: {e}\")\n", "            # 如果压缩检索失败，返回基础检索结果\n", "            return self.vectorSearch(query)\n", "\n", "if __name__ == \"__main__\":\n", "    # 测试修复后的代码\n", "    print(\"=== 测试修复后的ChatDoc类 ===\")\n", "    \n", "    chat_doc = ChatDoc()\n", "    chat_doc.doc = \"test.xlsx\"\n", "    print(\"=== 开始处理文档 ===\")\n", "    chat_doc.splitSentence()\n", "\n", "    print(\"\\n=== 测试不同的查询 ===\")\n", "\n", "    # 先测试一个更通用的查询\n", "    print(\"1. 测试通用查询：\")\n", "    unique_doc = chat_doc.askANdFindFiles(\"请假\")\n", "    print(f\"结果数量: {len(unique_doc)}\")\n", "    for i, doc in enumerate(unique_doc[:2]):\n", "        print(f\"结果 {i+1}: {doc.page_content[:200]}...\")\n", "\n", "    print(\"\\n2. 测试原始查询：\")\n", "    unique_doc = chat_doc.askANdFindFiles(\"找到付东明的数据\")\n", "    print(f\"结果数量: {len(unique_doc)}\")\n", "    if unique_doc:\n", "        for i, doc in enumerate(unique_doc):\n", "            print(f\"结果 {i+1}: {doc.page_content[:200]}...\")\n", "    else:\n", "        print(\"没有找到相关数据\") "]}], "metadata": {"kernelspec": {"display_name": "tushare", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}