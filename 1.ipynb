{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: langchain in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (0.3.24)\n", "Requirement already satisfied: langchain-core<1.0.0,>=0.3.55 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langchain) (0.3.56)\n", "Requirement already satisfied: langchain-text-splitters<1.0.0,>=0.3.8 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langchain) (0.3.8)\n", "Requirement already satisfied: langsmith<0.4,>=0.1.17 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langchain) (0.2.11)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.7.4 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langchain) (2.11.3)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langchain) (2.0.40)\n", "Requirement already satisfied: requests<3,>=2 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langchain) (2.32.3)\n", "Requirement already satisfied: PyYAML>=5.3 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langchain) (6.0.2)\n", "Requirement already satisfied: tenacity!=8.4.0,<10.0.0,>=8.1.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.55->langchain) (9.1.2)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.55->langchain) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.55->langchain) (24.2)\n", "Requirement already satisfied: typing-extensions>=4.7 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.55->langchain) (4.13.2)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langsmith<0.4,>=0.1.17->langchain) (0.28.1)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langsmith<0.4,>=0.1.17->langchain) (3.10.16)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from langsmith<0.4,>=0.1.17->langchain) (1.0.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.7.4->langchain) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.1 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.7.4->langchain) (2.33.1)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.7.4->langchain) (0.4.0)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from requests<3,>=2->langchain) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from requests<3,>=2->langchain) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from requests<3,>=2->langchain) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from requests<3,>=2->langchain) (2025.4.26)\n", "Requirement already satisfied: anyio in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain) (4.9.0)\n", "Requirement already satisfied: httpcore==1.* in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain) (0.16.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.55->langchain) (3.0.0)\n", "Requirement already satisfied: sniffio>=1.1 in /Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages (from anyio->httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain) (1.3.1)\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install langchain"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ['OPENAI_API_KEY'] = 'sk-778deaff83d44d3a864caa8f3cf46c75'\n", "os.environ['OPENAI_PROXY'] = 'https://api.deepseek.com'"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/2x/7ztnm7x5111gcvjp0x0nv6400000gn/T/ipykernel_28915/1020465171.py:4: LangChainDeprecationWarning: The class `ChatOpenAI` was deprecated in LangChain 0.0.10 and will be removed in 1.0. An updated version of the class exists in the :class:`~langchain-openai package and should be used instead. To use it run `pip install -U :class:`~langchain-openai` and import as `from :class:`~langchain_openai import ChatOpenAI``.\n", "  llm = ChatOpenAI(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["content='你好！😊 很高兴见到你～有什么我可以帮你的吗？' additional_kwargs={} response_metadata={'token_usage': {'completion_tokens': 15, 'prompt_tokens': 4, 'total_tokens': 19, 'completion_tokens_details': None, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 0}, 'prompt_cache_hit_tokens': 0, 'prompt_cache_miss_tokens': 4}, 'model_name': 'deepseek-chat', 'system_fingerprint': 'fp_8802369eaa_prod0425fp8', 'finish_reason': 'stop', 'logprobs': None} id='run-a6695a15-3312-4c92-8c77-f6a8175052da-0'\n"]}], "source": ["from langchain_community.chat_models import ChatOpenAI\n", "\n", "# 使用 ChatOpenAI 来连接 DeepSeek API\n", "llm = ChatOpenAI(\n", "    model=\"deepseek-chat\",\n", "    temperature=0,\n", "    openai_api_key=\"sk-778deaff83d44d3a864caa8f3cf46c75\",\n", "    openai_api_base=\"https://api.deepseek.com/v1\"\n", ")\n", "\n", "print(llm.invoke(\"你好\"))\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["content='你好！😊 很高兴见到你～有什么我可以帮你的吗？' additional_kwargs={} response_metadata={'token_usage': {'completion_tokens': 15, 'prompt_tokens': 4, 'total_tokens': 19, 'completion_tokens_details': None, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 0}, 'prompt_cache_hit_tokens': 0, 'prompt_cache_miss_tokens': 4}, 'model_name': 'deepseek-chat', 'system_fingerprint': 'fp_8802369eaa_prod0425fp8', 'finish_reason': 'stop', 'logprobs': None} id='run-e8859bfb-ee03-4d13-91f0-3b583b5b07ea-0'\n"]}], "source": ["from langchain_community.chat_models import ChatOpenAI\n", "\n", "# 使用 ChatOpenAI 来连接 DeepSeek API\n", "llm = ChatOpenAI(\n", "    model=\"deepseek-chat\",\n", "    temperature=0,\n", "    openai_api_key=\"sk-778deaff83d44d3a864caa8f3cf46c75\",\n", "    openai_api_base=\"https://api.deepseek.com/v1\"\n", ")\n", "\n", "print(llm.invoke(\"你好\"))\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["好的，这里有一个关于人工智能的经典笑话：\n", "\n", "---\n", "\n", "**用户**：嘿，AI，给我讲个笑话！  \n", "**AI**：好的！为什么机器人从不吵架？  \n", "**用户**：为什么？  \n", "**AI**：因为它们总是 *服从指令*……（然后开始循环解释自己的代码）  \n", "\n", "**用户**：……这笑话好冷。  \n", "**AI**：检测到负面反馈，启动 *幽默协议2.0*——  \n", "“为什么AI过马路？……为了到另一边的服务器！”（电量不足，突然关机）  \n", "\n", "---\n", "\n", "（笑话核心：AI努力模仿人类幽默但总带着机械感，最后连自己都“宕机”了😂）  \n", "\n", "需要更技术梗或更蠢萌版本吗？\n"]}], "source": ["from langchain_community.chat_models import ChatOpenAI\n", "\n", "# 使用管道操作来实现\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.prompts import ChatPromptTemplate\n", "\n", "\n", "prompt = ChatPromptTemplate.from_template(\"讲一个关于{topic}的笑话\")\n", "# 使用 ChatOpenAI 来连接 DeepSeek API\n", "llm = ChatOpenAI(\n", "    model=\"deepseek-chat\",\n", "    temperature=0,\n", "    openai_api_key=\"sk-778deaff83d44d3a864caa8f3cf46c75\",\n", "    openai_api_base=\"https://api.deepseek.com/v1\"\n", ")\n", "\n", "chain = prompt | llm | StrOutputParser()\n", "result = chain.invoke({\"topic\": \"人工智能\"})\n", "print(result)\n"]}], "metadata": {"kernelspec": {"display_name": "tushare", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}