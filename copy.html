<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI赋能开发 - 语音播报幻灯片</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* 性能优化 */
        html {
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
        }

        body {
            contain: layout style;
            will-change: scroll-position;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Microsoft YaHei', sans-serif;
            line-height: 1.7;
            color: #2c3e50;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" patternUnits="userSpaceOnUse" width="100" height="100"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.03)"/><circle cx="80" cy="80" r="1" fill="rgba(255,255,255,0.03)"/><circle cx="60" cy="30" r="1" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
            z-index: 0;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        .header {
            text-align: center;
            padding: 80px 0 60px 0;
            color: white;
            margin-bottom: 60px;
            position: relative;
            background: rgba(255,255,255,0.1);
            border-radius: 25px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .header::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #64748b, #475569, #334155, #64748b);
            border-radius: 25px;
            z-index: -1;
            animation: borderGlow 3s linear infinite;
        }

        @keyframes borderGlow {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 25px;
            text-shadow: 2px 2px 8px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #fff, #f0f8ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
            letter-spacing: -1px;
        }

        .header p {
            font-size: 1.4rem;
            opacity: 0.95;
            font-weight: 300;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.2);
        }

        .section {
            background: rgba(255, 255, 255, 0.98);
            margin: 40px 0;
            padding: 50px;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.3s ease;
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255,255,255,0.2);
            position: relative;
            overflow: hidden;
            will-change: transform;
            contain: layout style paint;
        }

        .section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #64748b, #475569, #334155);
        }

        .section:hover {
            transform: translate3d(0, -6px, 0) scale(1.005);
            box-shadow: 0 25px 60px rgba(0,0,0,0.12);
        }

        .section h2 {
            color: #475569;
            font-size: 2.5rem;
            margin-bottom: 30px;
            border-bottom: 3px solid #475569;
            padding-bottom: 15px;
        }

        .section h3 {
            color: #334155;
            font-size: 1.8rem;
            margin: 25px 0 15px 0;
        }

        .highlight-box {
            background: linear-gradient(135deg, #64748b 0%, #475569 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .tool-card {
            background: linear-gradient(135deg, #64748b 0%, #475569 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
        }

        .tool-card h4 {
            font-size: 1.4rem;
            margin-bottom: 15px;
        }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 25px;
            border-radius: 10px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }

        .xml-block {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            color: #e2e8f0;
            padding: 30px;
            border-radius: 15px;
            margin: 25px 0;
            font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.8;
            overflow-x: auto;
            border-left: 5px solid #64748b;
            border-top: 1px solid rgba(104, 211, 145, 0.3);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            white-space: pre-wrap;
            word-wrap: break-word;
            position: relative;
        }

        .xml-block::before {
            content: '📝 提示词模板';
            position: absolute;
            top: -12px;
            left: 20px;
            background: #64748b;
            color: #1a202c;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        }

        .xml-tag {
            color: #81c784;
        }

        .xml-content {
            color: #e3f2fd;
            margin-left: 20px;
        }

        .xml-comment {
            color: #90a4ae;
            font-style: italic;
        }

        .image-showcase {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .comparison-item {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }

        .comparison-item:hover {
            transform: translateY(-5px);
        }

        .image-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            gap: 10px;
        }

        .image-header h4 {
            color: #2c3e50;
            font-size: 1.2rem;
            margin: 0;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .status-indicator.success {
            background: #27ae60;
            box-shadow: 0 0 10px rgba(39, 174, 96, 0.5);
        }

        .status-indicator.error {
            background: #e74c3c;
            box-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
        }

        .image-wrapper {
            margin-bottom: 15px;
            overflow: hidden;
            border-radius: 15px;
        }

        .image-description {
            color: #34495e;
            font-size: 0.95rem;
            text-align: center;
            font-weight: 500;
            margin: 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #64748b;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .feature-card {
            background: linear-gradient(135deg, #64748b 0%, #475569 100%);
            color: white;
            padding: 35px 30px;
            border-radius: 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
            will-change: transform;
            contain: layout style paint;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .feature-card:hover::before {
            transform: translateX(100%);
        }

        .feature-card:hover {
            transform: translate3d(0, -6px, 0);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }

        .tips-list {
            background: #f7fafc;
            padding: 25px;
            border-radius: 10px;
            border-left: 5px solid #64748b;
        }

        .tips-list ul {
            list-style: none;
            padding-left: 0;
        }

        .tips-list li {
            padding: 8px 0;
            position: relative;
            padding-left: 25px;
        }

        .tips-list li:before {
            content: "✨";
            position: absolute;
            left: 0;
        }

        .workflow-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .step {
            background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            position: relative;
        }

        .step-number {
            background: #475569;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px auto;
            font-weight: bold;
        }

        .constraint-box {
            background: #fef5e7;
            border: 2px solid #f6ad55;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .constraint-box h4 {
            color: #c05621;
            margin-bottom: 15px;
        }

        .ai-challenges {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            border: 1px solid #cbd5e1;
        }

        .ai-challenges h3 {
            color: #334155;
            margin-bottom: 20px;
        }

        .footer {
            text-align: center;
            padding: 40px;
            color: white;
            margin-top: 50px;
        }

        /* 目录导航样式 */
        .table-of-contents {
            background: rgba(255, 255, 255, 0.95);
            margin: 40px 0;
            padding: 40px 50px;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1), 0 8px 25px rgba(0,0,0,0.08);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .table-of-contents h2 {
            color: #475569;
            font-size: 2.2rem;
            margin-bottom: 30px;
            text-align: center;
            border-bottom: 3px solid #475569;
            padding-bottom: 15px;
        }

        .toc-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .toc-item {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 12px;
            text-decoration: none;
            color: #334155;
            transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1), background 0.2s ease, box-shadow 0.2s ease;
            border-left: 4px solid #64748b;
            will-change: transform;
            contain: layout style;
        }

        .toc-item:hover {
            transform: translate3d(6px, 0, 0) scale(1.015);
            box-shadow: 0 6px 20px rgba(0,0,0,0.08);
            border-left-color: #475569;
            background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
            color: #1e293b;
        }

        .toc-item:focus {
            outline: 2px solid #64748b;
            outline-offset: 2px;
        }

        .toc-item:active {
            transform: translate3d(4px, 0, 0) scale(0.98);
        }

        .toc-number {
            background: #64748b;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
            font-size: 0.9rem;
            flex-shrink: 0;
        }

        .toc-content {
            flex: 1;
        }

        .toc-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 5px;
        }

        .toc-desc {
            font-size: 0.9rem;
            opacity: 0.8;
            margin: 0;
        }

        /* 幻灯片样式 */
        .slide-container {
            position: relative;
            width: 100%;
            height: 100vh;
            overflow: hidden;
        }

        .slide {
            display: none;
            width: 100%;
            height: 100vh;
            padding: 40px;
            box-sizing: border-box;
            overflow-y: auto;
            position: relative;
        }

        .slide.active {
            display: block;
        }

        /* 控制面板样式 */
        .controls {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            border-radius: 50px;
            padding: 15px 25px;
            display: flex;
            gap: 15px;
            align-items: center;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .control-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 10px 15px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .control-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
            z-index: 1001;
        }

        .slide-counter {
            color: white;
            font-size: 14px;
            font-weight: 500;
        }

        /* 语音控制样式 */
        .speech-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .speech-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .speech-btn.speaking {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* 键盘提示 */
        .keyboard-hint {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 10px;
            font-size: 12px;
            z-index: 1000;
            opacity: 0.7;
        }

        @media (max-width: 768px) {
            .slide {
                padding: 20px;
            }

            .controls {
                bottom: 10px;
                padding: 10px 15px;
                gap: 10px;
            }

            .control-btn {
                padding: 8px 12px;
                font-size: 12px;
            }

            .speech-btn {
                padding: 10px 15px;
                font-size: 12px;
            }

            .keyboard-hint {
                display: none;
            }

            .header h1 {
                font-size: 2.5rem;
            }

            .header p {
                font-size: 1.1rem;
            }

            .section {
                padding: 30px 20px;
                margin: 20px 0;
            }

            .section h2 {
                font-size: 2rem;
            }

            .comparison-container {
                grid-template-columns: 1fr !important;
                gap: 20px !important;
            }

            .tools-grid, .feature-grid {
                grid-template-columns: 1fr !important;
                gap: 20px !important;
            }

            .workflow-steps {
                grid-template-columns: 1fr !important;
                gap: 15px !important;
            }

            .step-number {
                width: 25px;
                height: 25px;
                font-size: 0.9rem;
            }

            .image-showcase {
                padding: 25px 15px;
            }

            .xml-block {
                padding: 20px 15px;
                font-size: 0.8rem;
            }

            .feature-card {
                padding: 25px 20px;
            }

            .feature-icon {
                font-size: 2.5rem;
            }

            .image-description {
                font-size: 0.9rem;
                padding: 12px;
            }

            .table-of-contents {
                padding: 25px 20px;
            }

            .toc-grid {
                grid-template-columns: 1fr !important;
                gap: 15px !important;
            }

            .toc-item {
                padding: 12px 15px;
            }

            .toc-number {
                width: 30px;
                height: 30px;
                margin-right: 12px;
                font-size: 0.8rem;
            }

            .toc-title {
                font-size: 1rem;
            }

            .toc-desc {
                font-size: 0.85rem;
            }

            .psychology-stages {
                grid-template-columns: 1fr !important;
                gap: 20px !important;
            }

            .stage-card {
                padding: 20px 15px !important;
            }

            .ai-psychology-journey {
                padding: 25px 15px !important;
            }

            .key-insight {
                padding: 20px 15px !important;
            }

            .key-insight p {
                font-size: 1rem !important;
            }
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'base',
            themeVariables: {
                primaryColor: '#64748b',
                primaryTextColor: '#1e293b',
                primaryBorderColor: '#475569',
                lineColor: '#64748b',
                secondaryColor: '#f1f5f9',
                tertiaryColor: '#e2e8f0',
                background: '#ffffff',
                mainBkg: '#ffffff',
                secondBkg: '#f8fafc',
                tertiaryBkg: '#f1f5f9'
            }
        });

        // 幻灯片控制系统
        class SlideShow {
            constructor() {
                this.currentSlide = 0;
                this.slides = [];
                this.speechSynthesis = window.speechSynthesis;
                this.isPlaying = false;
                this.autoPlay = false;
                this.init();
            }

            init() {
                this.createSlides();
                this.createControls();
                this.bindEvents();
                this.showSlide(0);
                this.updateProgress();
            }

            createSlides() {
                const container = document.querySelector('.container');
                const sections = container.querySelectorAll('.section, .header, .table-of-contents');

                // 创建幻灯片容器
                const slideContainer = document.createElement('div');
                slideContainer.className = 'slide-container';

                sections.forEach((section, index) => {
                    const slide = document.createElement('div');
                    slide.className = 'slide';
                    slide.appendChild(section.cloneNode(true));
                    slideContainer.appendChild(slide);
                    this.slides.push(slide);
                });

                // 替换原内容
                container.innerHTML = '';
                container.appendChild(slideContainer);
            }

            createControls() {
                // 进度条
                const progressBar = document.createElement('div');
                progressBar.className = 'progress-bar';
                document.body.appendChild(progressBar);

                // 键盘提示
                const keyboardHint = document.createElement('div');
                keyboardHint.className = 'keyboard-hint';
                keyboardHint.innerHTML = '快捷键: ← → 切换 | 空格 播放/暂停 | S 语音播报';
                document.body.appendChild(keyboardHint);

                // 控制面板
                const controls = document.createElement('div');
                controls.className = 'controls';
                controls.innerHTML = `
                    <button class="control-btn" id="prevBtn">
                        <span>⬅️</span> 上一页
                    </button>
                    <button class="speech-btn" id="speechBtn">
                        <span>🔊</span> 语音播报
                    </button>
                    <button class="control-btn" id="playBtn">
                        <span>▶️</span> 自动播放
                    </button>
                    <span class="slide-counter" id="slideCounter">1 / ${this.slides.length}</span>
                    <button class="control-btn" id="nextBtn">
                        下一页 <span>➡️</span>
                    </button>
                `;
                document.body.appendChild(controls);
            }

            bindEvents() {
                // 按钮事件
                document.getElementById('prevBtn').addEventListener('click', () => this.prevSlide());
                document.getElementById('nextBtn').addEventListener('click', () => this.nextSlide());
                document.getElementById('speechBtn').addEventListener('click', () => this.toggleSpeech());
                document.getElementById('playBtn').addEventListener('click', () => this.toggleAutoPlay());

                // 键盘事件
                document.addEventListener('keydown', (e) => {
                    switch(e.key) {
                        case 'ArrowLeft':
                            e.preventDefault();
                            this.prevSlide();
                            break;
                        case 'ArrowRight':
                        case ' ':
                            e.preventDefault();
                            if (e.key === ' ' && this.autoPlay) {
                                this.toggleAutoPlay();
                            } else {
                                this.nextSlide();
                            }
                            break;
                        case 's':
                        case 'S':
                            e.preventDefault();
                            this.toggleSpeech();
                            break;
                        case 'Escape':
                            this.stopSpeech();
                            this.autoPlay = false;
                            this.updatePlayButton();
                            break;
                    }
                });

                // 语音结束事件
                this.speechSynthesis.addEventListener('voiceschanged', () => {
                    this.voices = this.speechSynthesis.getVoices();
                });
            }

            showSlide(index) {
                this.slides.forEach((slide, i) => {
                    slide.classList.toggle('active', i === index);
                });
                this.currentSlide = index;
                this.updateControls();
                this.updateProgress();

                // 重新初始化 Mermaid 图表
                setTimeout(() => {
                    mermaid.init();
                }, 100);
            }

            nextSlide() {
                if (this.currentSlide < this.slides.length - 1) {
                    this.showSlide(this.currentSlide + 1);
                } else if (this.autoPlay) {
                    this.autoPlay = false;
                    this.updatePlayButton();
                }
            }

            prevSlide() {
                if (this.currentSlide > 0) {
                    this.showSlide(this.currentSlide - 1);
                }
            }

            updateControls() {
                const prevBtn = document.getElementById('prevBtn');
                const nextBtn = document.getElementById('nextBtn');
                const slideCounter = document.getElementById('slideCounter');

                prevBtn.disabled = this.currentSlide === 0;
                nextBtn.disabled = this.currentSlide === this.slides.length - 1;
                slideCounter.textContent = `${this.currentSlide + 1} / ${this.slides.length}`;
            }

            updateProgress() {
                const progressBar = document.querySelector('.progress-bar');
                const progress = ((this.currentSlide + 1) / this.slides.length) * 100;
                progressBar.style.width = `${progress}%`;
            }

            toggleSpeech() {
                if (this.isPlaying) {
                    this.stopSpeech();
                } else {
                    this.startSpeech();
                }
            }

            startSpeech() {
                const currentSlideElement = this.slides[this.currentSlide];
                const text = this.extractTextFromSlide(currentSlideElement);

                if (text.trim()) {
                    const utterance = new SpeechSynthesisUtterance(text);

                    // 设置中文语音
                    const voices = this.speechSynthesis.getVoices();
                    const chineseVoice = voices.find(voice =>
                        voice.lang.includes('zh') || voice.name.includes('Chinese')
                    );
                    if (chineseVoice) {
                        utterance.voice = chineseVoice;
                    }

                    utterance.rate = 0.9;
                    utterance.pitch = 1;
                    utterance.volume = 1;

                    utterance.onstart = () => {
                        this.isPlaying = true;
                        this.updateSpeechButton();
                    };

                    utterance.onend = () => {
                        this.isPlaying = false;
                        this.updateSpeechButton();

                        if (this.autoPlay && this.currentSlide < this.slides.length - 1) {
                            setTimeout(() => {
                                this.nextSlide();
                                setTimeout(() => this.startSpeech(), 500);
                            }, 1000);
                        }
                    };

                    this.speechSynthesis.speak(utterance);
                }
            }

            stopSpeech() {
                this.speechSynthesis.cancel();
                this.isPlaying = false;
                this.updateSpeechButton();
            }

            extractTextFromSlide(slideElement) {
                const clone = slideElement.cloneNode(true);

                // 移除不需要朗读的元素
                const elementsToRemove = clone.querySelectorAll('script, style, .mermaid, .toc-number, .feature-icon');
                elementsToRemove.forEach(el => el.remove());

                // 获取文本内容
                let text = clone.textContent || clone.innerText || '';

                // 清理文本
                text = text.replace(/\s+/g, ' ').trim();
                text = text.replace(/([。！？])\s*/g, '$1 '); // 在句号后添加停顿

                return text;
            }

            updateSpeechButton() {
                const speechBtn = document.getElementById('speechBtn');
                if (this.isPlaying) {
                    speechBtn.innerHTML = '<span>⏸️</span> 停止播报';
                    speechBtn.classList.add('speaking');
                } else {
                    speechBtn.innerHTML = '<span>🔊</span> 语音播报';
                    speechBtn.classList.remove('speaking');
                }
            }

            toggleAutoPlay() {
                this.autoPlay = !this.autoPlay;
                this.updatePlayButton();

                if (this.autoPlay && !this.isPlaying) {
                    this.startSpeech();
                }
            }

            updatePlayButton() {
                const playBtn = document.getElementById('playBtn');
                if (this.autoPlay) {
                    playBtn.innerHTML = '<span>⏸️</span> 停止自动';
                    playBtn.style.background = 'linear-gradient(45deg, #e74c3c, #c0392b)';
                } else {
                    playBtn.innerHTML = '<span>▶️</span> 自动播放';
                    playBtn.style.background = 'rgba(255, 255, 255, 0.2)';
                }
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            new SlideShow();
        });
    </script>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🚀 AI辅助编程</h1>
            <p>大幅提升个人生产率，降低编程语言学习门槛</p>
        </header>

        <section class="section" id="ai-psychology" style="margin-top: 40px;">
            <h2>🧠 人类对AI认识的心理变化历程</h2>
            
            <div class="ai-psychology-journey" style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); padding: 40px; border-radius: 20px; margin: 30px 0; border: 1px solid rgba(100, 116, 139, 0.1);">
                <div class="mermaid-container" style="text-align: center; margin: 30px 0;">
                    <div class="mermaid">
graph LR
    A["🤔 初识阶段<br/>「AI是什么？」<br/>• 好奇与困惑<br/>• 媒体炒作影响<br/>• 期望过高"] --> B["📚 了解阶段<br/>「AI能做什么？」<br/>• 理性认知<br/>• 实际体验<br/>• 发现局限性"]
    
    B --> C["🔍 深入阶段<br/>「如何用好AI？」<br/>• 掌握技巧<br/>• 工作流整合<br/>• 持续优化"]
    
    C --> D["🚀 精通阶段<br/>「AI赋能创新」<br/>• 战略思维<br/>• 生态构建<br/>• 引领变革"]
    
    A -.-> A1["心理特征:<br/>焦虑、兴奋、迷茫"]
    B -.-> B1["心理特征:<br/>理性、务实、探索"]
    C -.-> C1["心理特征:<br/>自信、专注、系统化"]
    D -.-> D1["心理特征:<br/>前瞻、创新、领导力"]
    
    style A fill:#ffebee,stroke:#e57373,stroke-width:3px,color:#000
    style B fill:#e8f5e8,stroke:#81c784,stroke-width:3px,color:#000
    style C fill:#e3f2fd,stroke:#64b5f6,stroke-width:3px,color:#000
    style D fill:#f3e5f5,stroke:#ba68c8,stroke-width:3px,color:#000
    
    style A1 fill:#fff3e0,stroke:#ffb74d,stroke-width:2px,color:#000
    style B1 fill:#fff3e0,stroke:#ffb74d,stroke-width:2px,color:#000
    style C1 fill:#fff3e0,stroke:#ffb74d,stroke-width:2px,color:#000
    style D1 fill:#fff3e0,stroke:#ffb74d,stroke-width:2px,color:#000
                    </div>
                </div>
                
                <div class="psychology-stages" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 25px; margin-top: 40px;">
                    <div class="stage-card" style="background: rgba(255, 235, 238, 0.8); padding: 25px; border-radius: 15px; border-left: 5px solid #e57373;">
                        <h4 style="color: #c62828; margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
                            <span style="font-size: 1.5rem;">🤔</span> 初识阶段
                        </h4>
                        <p style="color: #424242; margin-bottom: 10px;"><strong>核心问题：</strong>"AI是什么？"</p>
                        <ul style="color: #616161; font-size: 0.95rem; line-height: 1.6;">
                            <li>对AI充满好奇但理解模糊</li>
                            <li>容易被媒体报道影响判断</li>
                            <li>期望值往往过高或过低</li>
                            <li>心理状态：焦虑、兴奋、迷茫交织</li>
                        </ul>
                    </div>
                    
                    <div class="stage-card" style="background: rgba(232, 245, 232, 0.8); padding: 25px; border-radius: 15px; border-left: 5px solid #81c784;">
                        <h4 style="color: #2e7d32; margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
                            <span style="font-size: 1.5rem;">📚</span> 了解阶段
                        </h4>
                        <p style="color: #424242; margin-bottom: 10px;"><strong>核心问题：</strong>"AI能做什么？"</p>
                        <ul style="color: #616161; font-size: 0.95rem; line-height: 1.6;">
                            <li>开始理性认识AI的能力边界</li>
                            <li>通过实际体验了解AI工具</li>
                            <li>逐渐发现AI的局限性</li>
                            <li>心理状态：理性、务实、积极探索</li>
                        </ul>
                    </div>
                    
                    <div class="stage-card" style="background: rgba(227, 242, 253, 0.8); padding: 25px; border-radius: 15px; border-left: 5px solid #64b5f6;">
                        <h4 style="color: #1565c0; margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
                            <span style="font-size: 1.5rem;">🔍</span> 深入阶段
                        </h4>
                        <p style="color: #424242; margin-bottom: 10px;"><strong>核心问题：</strong>"如何用好AI？"</p>
                        <ul style="color: #616161; font-size: 0.95rem; line-height: 1.6;">
                            <li>掌握提示词工程等核心技巧</li>
                            <li>将AI整合到日常工作流程</li>
                            <li>持续优化AI使用效率</li>
                            <li>心理状态：自信、专注、系统化思考</li>
                        </ul>
                    </div>
                    
                    <div class="stage-card" style="background: rgba(243, 229, 245, 0.8); padding: 25px; border-radius: 15px; border-left: 5px solid #ba68c8;">
                        <h4 style="color: #7b1fa2; margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
                            <span style="font-size: 1.5rem;">🚀</span> 精通阶段
                        </h4>
                        <p style="color: #424242; margin-bottom: 10px;"><strong>核心问题：</strong>"AI如何赋能创新？"</p>
                        <ul style="color: #616161; font-size: 0.95rem; line-height: 1.6;">
                            <li>具备AI战略思维和前瞻视野</li>
                            <li>构建完整的AI应用生态</li>
                            <li>引领团队和行业的AI变革</li>
                            <li>心理状态：前瞻、创新、具备领导力</li>
                        </ul>
                    </div>
                </div>
                
                <div class="key-insight" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 15px; margin-top: 30px; text-align: center;">
                    <h4 style="color: white; margin-bottom: 15px; font-size: 1.3rem;">💡 关键洞察</h4>
                    <p style="font-size: 1.1rem; margin: 0; line-height: 1.6;">
                        每个人对AI的认知都会经历这四个阶段，<strong>关键是要保持开放心态</strong>，<br>
                        既不盲目恐惧，也不过度乐观，而是在实践中逐步建立正确的AI认知框架
                    </p>
                </div>
            </div>
        </section>

        <div class="table-of-contents">
            <h2>📋 演示目录</h2>
            <div class="toc-grid">
                <a href="#ai-psychology" class="toc-item" tabindex="0">
                    <div class="toc-number">00</div>
                    <div class="toc-content">
                        <div class="toc-title">🧠 心理变化历程</div>
                        <p class="toc-desc">人类对AI认识的四个阶段</p>
                    </div>
                </a>
                
                <a href="#core-concepts" class="toc-item" tabindex="0">
                    <div class="toc-number">01</div>
                    <div class="toc-content">
                        <div class="toc-title">💡 核心理念</div>
                        <p class="toc-desc">AI辅助编程的基本概念与价值</p>
                    </div>
                </a>
                
                <a href="#ai-tools" class="toc-item" tabindex="0">
                    <div class="toc-number">02</div>
                    <div class="toc-content">
                        <div class="toc-title">🛠️ 工具全景</div>
                        <p class="toc-desc">主流AI编程工具选择指南</p>
                    </div>
                </a>
                
                <a href="#web-tools" class="toc-item" tabindex="0">
                    <div class="toc-number">03</div>
                    <div class="toc-content">
                        <div class="toc-title">🌐 网页生成</div>
                        <p class="toc-desc">AI驱动的网页开发平台</p>
                    </div>
                </a>
                
                <a href="#prompt-engineering" class="toc-item" tabindex="0">
                    <div class="toc-number">04</div>
                    <div class="toc-content">
                        <div class="toc-title">🔮 提示词工程</div>
                        <p class="toc-desc">从理论到实战模板库</p>
                    </div>
                </a>
                
                <a href="#cursor-usage" class="toc-item" tabindex="0">
                    <div class="toc-number">05</div>
                    <div class="toc-content">
                        <div class="toc-title">⚙️ Cursor实战</div>
                        <p class="toc-desc">工作模式与使用技巧</p>
                    </div>
                </a>
                
                <a href="#ai-constraints" class="toc-item" tabindex="0">
                    <div class="toc-number">06</div>
                    <div class="toc-content">
                        <div class="toc-title">🎯 技术约束</div>
                        <p class="toc-desc">大模型限制与AI友好架构</p>
                    </div>
                </a>
                
                <a href="#opportunities" class="toc-item" tabindex="0">
                    <div class="toc-number">07</div>
                    <div class="toc-content">
                        <div class="toc-title">🌊 机遇挑战</div>
                        <p class="toc-desc">AI时代的生存与发展策略</p>
                    </div>
                </a>
                
                <a href="#learning-guide" class="toc-item" tabindex="0">
                    <div class="toc-number">08</div>
                    <div class="toc-content">
                        <div class="toc-title">🔍 学习建议</div>
                        <p class="toc-desc">获取AI行业信息的方法</p>
                    </div>
                </a>
                
                <a href="#private-components" class="toc-item" tabindex="0">
                    <div class="toc-number">09</div>
                    <div class="toc-content">
                        <div class="toc-title">🏗️ 私有组件库</div>
                        <p class="toc-desc">企业级AI开发解决方案</p>
                    </div>
                </a>
                
                <a href="#action-guide" class="toc-item" tabindex="0">
                    <div class="toc-number">10</div>
                    <div class="toc-content">
                        <div class="toc-title">🎯 行动指南</div>
                        <p class="toc-desc">立即开始AI编程之旅</p>
                    </div>
                </a>
            </div>
        </div>

        <section class="section" id="core-concepts">
            <h2>💡 AI辅助编程的核心理念</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <span class="feature-icon">🚀</span>
                    <h3>生产力革命</h3>
                    <p>AI辅助编程能够将开发效率提升0.1-5倍，让程序员专注于创意和架构设计，而非重复性编码工作</p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">🎓</span>
                    <h3>学习门槛降低</h3>
                    <p>通过智能代码提示和错误修复，新手可以更快上手编程，老手可以轻松掌握新技术栈</p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">⚖️</span>
                    <h3>正确认知定位</h3>
                    <p>AI是强大的助手而非万能工具，需要在保持理性的同时积极拥抱技术变革</p>
                </div>
            </div>
            
            <div class="highlight-box">
                <h3>🎯 AI辅助编程的黄金法则</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 25px;">
                    <div style="text-align: center; padding: 20px;">
                        <div style="font-size: 2rem; margin-bottom: 15px;">🛠️</div>
                        <h4>优秀工具</h4>
                        <p>选择合适的AI编程工具</p>
                    </div>
                    <div style="text-align: center; padding: 20px;">
                        <div style="font-size: 2rem; margin-bottom: 15px;">📋</div>
                        <h4>结构化指令</h4>
                        <p>掌握提示词工程技巧</p>
                    </div>
                    <div style="text-align: center; padding: 20px;">
                        <div style="font-size: 2rem; margin-bottom: 15px;">🏗️</div>
                        <h4>模块化编程</h4>
                        <p>构建AI友好的代码架构</p>
                    </div>
                    <div style="text-align: center; padding: 20px;">
                        <div style="font-size: 2rem; margin-bottom: 15px;">🧘</div>
                        <h4>理性态度</h4>
                        <p>保持学习心态，不焦虑</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="section" id="ai-tools">
            <h2>🛠️ AI编程工具全景图：选择适合你的AI助手</h2>
            
            <div class="highlight-box">
                <h3>🎯 工具选择的核心原则</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-top: 25px;">
                    <div style="text-align: center; padding: 15px;">
                        <div style="font-size: 2rem; margin-bottom: 10px;">🎯</div>
                        <h4>明确需求</h4>
                        <p>代码补全 vs 全功能助手</p>
                    </div>
                    <div style="text-align: center; padding: 15px;">
                        <div style="font-size: 2rem; margin-bottom: 10px;">💰</div>
                        <h4>预算考虑</h4>
                        <p>免费 vs 付费 vs 企业级</p>
                    </div>
                    <div style="text-align: center; padding: 15px;">
                        <div style="font-size: 2rem; margin-bottom: 10px;">🔒</div>
                        <h4>安全合规</h4>
                        <p>数据隐私与企业要求</p>
                    </div>
                    <div style="text-align: center; padding: 15px;">
                        <div style="font-size: 2rem; margin-bottom: 10px;">🔧</div>
                        <h4>生态适配</h4>
                        <p>IDE支持与技术栈匹配</p>
                    </div>
                </div>
            </div>

        <section class="section">
            <h2>🛠️ 主流AI编程工具完整列表</h2>
            
            <div class="tools-table-container" style="overflow-x: auto; margin: 20px 0; border-radius: 15px; box-shadow: 0 8px 25px rgba(0,0,0,0.1);">
                <table class="ai-tools-table" style="width: 100%; border-collapse: collapse; background: white; font-size: 14px;">
                    <thead>
                        <tr style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                            <th style="padding: 15px 10px; text-align: left; border: none; font-weight: 600;">🛠️ 工具名称</th>
                            <th style="padding: 15px 10px; text-align: left; border: none; font-weight: 600;">📁 类型</th>
                            <th style="padding: 15px 10px; text-align: left; border: none; font-weight: 600;">✨ 优势亮点</th>
                            <th style="padding: 15px 10px; text-align: center; border: none; font-weight: 600;">🌐 官方网站</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="border-bottom: 1px solid #e2e8f0;">
                            <td style="padding: 12px 10px; font-weight: 600; color: #2d3748;">Continue</td>
                            <td style="padding: 12px 10px; color: #4a5568;">开源 AI 代码助手</td>
                            <td style="padding: 12px 10px; color: #4a5568;">支持VS Code和JetBrains，自定义AI助手，代码自动完成</td>
                            <td style="padding: 12px 10px; text-align: center;">
                                <a href="https://continue.dev" target="_blank" style="color: #667eea; text-decoration: none; font-weight: 600; padding: 6px 12px; border: 1px solid #667eea; border-radius: 6px; transition: all 0.3s;">访问官网</a>
                            </td>
                        </tr>
                        <tr style="border-bottom: 1px solid #e2e8f0; background: #f8fafc;">
                            <td style="padding: 12px 10px; font-weight: 600; color: #2d3748;">Cline</td>
                            <td style="padding: 12px 10px; color: #4a5568;">VS Code 扩展</td>
                            <td style="padding: 12px 10px; color: #4a5568;">AI自主编码代理，文件创建/编辑，终端命令执行</td>
                            <td style="padding: 12px 10px; text-align: center;">
                                <a href="https://cline.bot" target="_blank" style="color: #667eea; text-decoration: none; font-weight: 600; padding: 6px 12px; border: 1px solid #667eea; border-radius: 6px; transition: all 0.3s;">访问官网</a>
                            </td>
                        </tr>
                        <tr style="border-bottom: 1px solid #e2e8f0;">
                            <td style="padding: 12px 10px; font-weight: 600; color: #2d3748;">Cursor</td>
                            <td style="padding: 12px 10px; color: #4a5568;">独立 AI 编程 IDE</td>
                            <td style="padding: 12px 10px; color: #4a5568;">Tab自动完成，聊天功能，自然语言编辑，代理模式</td>
                            <td style="padding: 12px 10px; text-align: center;">
                                <a href="https://cursor.com" target="_blank" style="color: #667eea; text-decoration: none; font-weight: 600; padding: 6px 12px; border: 1px solid #667eea; border-radius: 6px; transition: all 0.3s;">访问官网</a>
                            </td>
                        </tr>
                        <tr style="border-bottom: 1px solid #e2e8f0; background: #f8fafc;">
                            <td style="padding: 12px 10px; font-weight: 600; color: #2d3748;">GitHub Copilot</td>
                            <td style="padding: 12px 10px; color: #4a5568;">IDE 插件</td>
                            <td style="padding: 12px 10px; color: #4a5568;">由OpenAI支持，实时代码建议，多语言支持，深度IDE集成</td>
                            <td style="padding: 12px 10px; text-align: center;">
                                <a href="https://github.com/features/copilot" target="_blank" style="color: #667eea; text-decoration: none; font-weight: 600; padding: 6px 12px; border: 1px solid #667eea; border-radius: 6px; transition: all 0.3s;">访问官网</a>
                            </td>
                        </tr>
                        <tr style="border-bottom: 1px solid #e2e8f0;">
                            <td style="padding: 12px 10px; font-weight: 600; color: #2d3748;">Tabnine</td>
                            <td style="padding: 12px 10px; color: #4a5568;">IDE 插件</td>
                            <td style="padding: 12px 10px; color: #4a5568;">私有化部署，代码补全，安全合规，支持多种IDE</td>
                            <td style="padding: 12px 10px; text-align: center;">
                                <a href="https://www.tabnine.com" target="_blank" style="color: #667eea; text-decoration: none; font-weight: 600; padding: 6px 12px; border: 1px solid #667eea; border-radius: 6px; transition: all 0.3s;">访问官网</a>
                            </td>
                        </tr>
                        <tr style="border-bottom: 1px solid #e2e8f0; background: #f8fafc;">
                            <td style="padding: 12px 10px; font-weight: 600; color: #2d3748;">Qodo</td>
                            <td style="padding: 12px 10px; color: #4a5568;">测试生成与审查工具</td>
                            <td style="padding: 12px 10px; color: #4a5568;">代码质量检测，智能测试生成，AI代码审查</td>
                            <td style="padding: 12px 10px; text-align: center;">
                                <a href="https://www.qodo.ai" target="_blank" style="color: #667eea; text-decoration: none; font-weight: 600; padding: 6px 12px; border: 1px solid #667eea; border-radius: 6px; transition: all 0.3s;">访问官网</a>
                            </td>
                        </tr>
                        <tr style="border-bottom: 1px solid #e2e8f0;">
                            <td style="padding: 12px 10px; font-weight: 600; color: #2d3748;">Windsurf</td>
                            <td style="padding: 12px 10px; color: #4a5568;">AI 原生 IDE</td>
                            <td style="padding: 12px 10px; color: #4a5568;">AI代理Cascade，上下文感知，实时预览部署</td>
                            <td style="padding: 12px 10px; text-align: center;">
                                <a href="https://windsurf.com" target="_blank" style="color: #667eea; text-decoration: none; font-weight: 600; padding: 6px 12px; border: 1px solid #667eea; border-radius: 6px; transition: all 0.3s;">访问官网</a>
                            </td>
                        </tr>
                        <tr style="border-bottom: 1px solid #e2e8f0; background: #f8fafc;">
                            <td style="padding: 12px 10px; font-weight: 600; color: #2d3748;">Amazon CodeWhisperer</td>
                            <td style="padding: 12px 10px; color: #4a5568;">AWS 集成插件</td>
                            <td style="padding: 12px 10px; color: #4a5568;">AWS专家级AI，ML代码建议，安全扫描，多语言支持</td>
                            <td style="padding: 12px 10px; text-align: center;">
                                <a href="https://aws.amazon.com/codewhisperer" target="_blank" style="color: #667eea; text-decoration: none; font-weight: 600; padding: 6px 12px; border: 1px solid #667eea; border-radius: 6px; transition: all 0.3s;">访问官网</a>
                            </td>
                        </tr>
                        <tr style="border-bottom: 1px solid #e2e8f0;">
                            <td style="padding: 12px 10px; font-weight: 600; color: #2d3748;">AskCodi</td>
                            <td style="padding: 12px 10px; color: #4a5568;">辅助教学编码工具</td>
                            <td style="padding: 12px 10px; color: #4a5568;">代码生成器，重构工具，错误修复，学习辅助</td>
                            <td style="padding: 12px 10px; text-align: center;">
                                <a href="https://www.askcodi.com" target="_blank" style="color: #667eea; text-decoration: none; font-weight: 600; padding: 6px 12px; border: 1px solid #667eea; border-radius: 6px; transition: all 0.3s;">访问官网</a>
                            </td>
                        </tr>
                        <tr style="border-bottom: 1px solid #e2e8f0; background: #f8fafc;">
                            <td style="padding: 12px 10px; font-weight: 600; color: #2d3748;">JetBrains AI Assistant</td>
                            <td style="padding: 12px 10px; color: #4a5568;">JetBrains 插件</td>
                            <td style="padding: 12px 10px; color: #4a5568;">深度IDE集成，上下文感知，多语言模型支持</td>
                            <td style="padding: 12px 10px; text-align: center;">
                                <a href="https://www.jetbrains.com/ai-assistant" target="_blank" style="color: #667eea; text-decoration: none; font-weight: 600; padding: 6px 12px; border: 1px solid #667eea; border-radius: 6px; transition: all 0.3s;">访问官网</a>
                            </td>
                        </tr>
                        <tr style="border-bottom: 1px solid #e2e8f0;">
                            <td style="padding: 12px 10px; font-weight: 600; color: #2d3748;">Replit</td>
                            <td style="padding: 12px 10px; color: #4a5568;">在线IDE</td>
                            <td style="padding: 12px 10px; color: #4a5568;">云端开发环境，AI代理，自然语言编程，即时部署</td>
                            <td style="padding: 12px 10px; text-align: center;">
                                <a href="https://replit.com" target="_blank" style="color: #667eea; text-decoration: none; font-weight: 600; padding: 6px 12px; border: 1px solid #667eea; border-radius: 6px; transition: all 0.3s;">访问官网</a>
                            </td>
                        </tr>
                        <tr style="border-bottom: 1px solid #e2e8f0; background: #f8fafc;">
                            <td style="padding: 12px 10px; font-weight: 600; color: #2d3748;">Gemini Code Assist</td>
                            <td style="padding: 12px 10px; color: #4a5568;">Google AI 编程助手</td>
                            <td style="padding: 12px 10px; color: #4a5568;">Google Gemini模型驱动，智能代码生成，云原生开发支持</td>
                            <td style="padding: 12px 10px; text-align: center;">
                                <a href="https://cloud.google.com/products/ai/code-assist" target="_blank" style="color: #667eea; text-decoration: none; font-weight: 600; padding: 6px 12px; border: 1px solid #667eea; border-radius: 6px; transition: all 0.3s;">访问官网</a>
                            </td>
                        </tr>
                        <tr style="border-bottom: 1px solid #e2e8f0;">
                            <td style="padding: 12px 10px; font-weight: 600; color: #2d3748;">腾讯云AI代码助手</td>
                            <td style="padding: 12px 10px; color: #4a5568;">腾讯云原生AI工具</td>
                            <td style="padding: 12px 10px; color: #4a5568;">微信小程序开发专家，腾讯云生态深度集成，中文优化</td>
                            <td style="padding: 12px 10px; text-align: center;">
                                <a href="https://copilot.tencent.com/setup/wx-code/" target="_blank" style="color: #667eea; text-decoration: none; font-weight: 600; padding: 6px 12px; border: 1px solid #667eea; border-radius: 6px; transition: all 0.3s;">访问官网</a>
                            </td>
                        </tr>
                        <tr style="background: #f8fafc;">
                            <td style="padding: 12px 10px; font-weight: 600; color: #2d3748;">通义灵码</td>
                            <td style="padding: 12px 10px; color: #4a5568;">阿里云AI编程助手</td>
                            <td style="padding: 12px 10px; color: #4a5568;">阿里通义千问模型，中文代码理解，企业级安全合规</td>
                            <td style="padding: 12px 10px; text-align: center;">
                                <a href="https://tongyi.aliyun.com/lingma" target="_blank" style="color: #667eea; text-decoration: none; font-weight: 600; padding: 6px 12px; border: 1px solid #667eea; border-radius: 6px; transition: all 0.3s;">访问官网</a>
                            </td>
                        </tr>

                    </tbody>
                </table>
            </div>
            
            <div class="tools-summary" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 25px; border-radius: 15px; margin: 25px 0; text-align: center;">
                <h4 style="color: white; margin-bottom: 15px;">💡 工具选择建议</h4>
                <p style="margin: 0; font-size: 1.1rem;">选择AI编程工具时，建议根据团队规模、技术栈、预算和安全要求进行综合考虑。<br>
                <strong>推荐：</strong>GitHub Copilot、Cursor &nbsp;&nbsp;
                <strong>企业级：</strong>Tabnine、JetBrains AI Assistant &nbsp;&nbsp;
                <strong>开源偏好：</strong>Continue &nbsp;&nbsp;
            </div>
        </section>

        <section class="section" id="web-tools">
            <h2>🌐 AI网页生成工具</h2>
            
            <div class="tools-table-container" style="overflow-x: auto; margin: 20px 0; border-radius: 15px; box-shadow: 0 8px 25px rgba(0,0,0,0.1);">
                <table class="ai-tools-table" style="width: 100%; border-collapse: collapse; background: white; font-size: 14px;">
                    <thead>
                        <tr style="background: linear-gradient(135deg, #8e2de2 0%, #4a00e0 100%); color: white;">
                            <th style="padding: 15px 10px; text-align: left; border: none; font-weight: 600;">🛠️ 平台名称</th>
                            <th style="padding: 15px 10px; text-align: left; border: none; font-weight: 600;">🎯 核心定位</th>
                            <th style="padding: 15px 10px; text-align: left; border: none; font-weight: 600;">✨ 主要特色</th>
                            <th style="padding: 15px 10px; text-align: center; border: none; font-weight: 600;">🌐 官方网站</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="border-bottom: 1px solid #e2e8f0;">
                            <td style="padding: 12px 10px; font-weight: 600; color: #2d3748;">v0.dev</td>
                            <td style="padding: 12px 10px; color: #4a5568;">Vercel AI驱动的UI生成器</td>
                            <td style="padding: 12px 10px; color: #4a5568;">自然语言生成React组件，支持TailwindCSS，与Next.js深度集成</td>
                            <td style="padding: 12px 10px; text-align: center;">
                                <a href="https://v0.dev/" target="_blank" style="color: #8e2de2; text-decoration: none; font-weight: 600; padding: 6px 12px; border: 1px solid #8e2de2; border-radius: 6px; transition: all 0.3s;">访问官网</a>
                            </td>
                        </tr>
                        <tr style="border-bottom: 1px solid #e2e8f0; background: #f8fafc;">
                            <td style="padding: 12px 10px; font-weight: 600; color: #2d3748;">Bolt.new</td>
                            <td style="padding: 12px 10px; color: #4a5568;">浏览器内全栈开发平台</td>
                            <td style="padding: 12px 10px; color: #4a5568;">在线完整开发环境，支持多种框架，即时预览部署</td>
                            <td style="padding: 12px 10px; text-align: center;">
                                <a href="https://bolt.new/" target="_blank" style="color: #8e2de2; text-decoration: none; font-weight: 600; padding: 6px 12px; border: 1px solid #8e2de2; border-radius: 6px; transition: all 0.3s;">访问官网</a>
                            </td>
                        </tr>
                        <tr style="border-bottom: 1px solid #e2e8f0;">
                            <td style="padding: 12px 10px; font-weight: 600; color: #2d3748;">Lovable</td>
                            <td style="padding: 12px 10px; color: #4a5568;">AI驱动的应用构建平台</td>
                            <td style="padding: 12px 10px; color: #4a5568;">支持Figma导入，团队协作，从原型到生产的完整流程</td>
                            <td style="padding: 12px 10px; text-align: center;">
                                <a href="https://lovable.dev/" target="_blank" style="color: #8e2de2; text-decoration: none; font-weight: 600; padding: 6px 12px; border: 1px solid #8e2de2; border-radius: 6px; transition: all 0.3s;">访问官网</a>
                            </td>
                        </tr>
                        <tr style="background: #f8fafc;">
                            <td style="padding: 12px 10px; font-weight: 600; color: #2d3748;">NoCode.cn</td>
                            <td style="padding: 12px 10px; color: #4a5568;">中文无代码开发平台</td>
                            <td style="padding: 12px 10px; color: #4a5568;">面向中文用户的无代码解决方案，本土化服务支持</td>
                            <td style="padding: 12px 10px; text-align: center;">
                                <a href="https://nocode.cn/" target="_blank" style="color: #8e2de2; text-decoration: none; font-weight: 600; padding: 6px 12px; border: 1px solid #8e2de2; border-radius: 6px; transition: all 0.3s;">访问官网</a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="tools-summary" style="background: linear-gradient(135deg, #8e2de2 0%, #4a00e0 100%); color: white; padding: 25px; border-radius: 15px; margin: 25px 0; text-align: center;">
                <h4 style="color: white; margin-bottom: 15px;">🚀 网页生成工具选择指南</h4>
                <p style="margin: 0; font-size: 1.1rem;">这些AI驱动的网页生成工具正在重新定义前端开发方式。<br>
                <strong>设计师推荐：</strong>v0.dev + Lovable &nbsp;&nbsp;
                <strong>全栈开发：</strong>Bolt.new &nbsp;&nbsp;
            </div>

            <div class="feature-grid">
                <div class="feature-card" style="background: linear-gradient(135deg, #8e2de2 0%, #4a00e0 100%);">
                    <span class="feature-icon">⚡</span>
                    <h3>开发效率革命</h3>
                    <p>通过自然语言描述即可生成完整的网页应用，将开发周期从周缩短到分钟</p>
                </div>
                <div class="feature-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <span class="feature-icon">🎨</span>
                    <h3>设计即代码</h3>
                    <p>支持从设计稿直接生成高质量代码，保持设计与开发的完美同步</p>
                </div>
                <div class="feature-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                    <span class="feature-icon">🌍</span>
                    <h3>云端协作</h3>
                    <p>基于云端的开发环境，支持团队实时协作和版本管理</p>
                </div>
            </div>
        </section>

        <section class="section" id="cursor-usage">
            <h2>⚙️ Cursor工作模式</h2>
            
            <div class="constraint-box">
                <h4>三种对话模式的应用场景</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 20px;">
                    <div style="background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #64748b;">
                        <strong>Ask模式</strong><br>
                        用于咨询、代码解读
                    </div>
                    <div style="background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #475569;">
                        <strong>Manual模式</strong><br>
                        用于修复单个文件细节问题
                    </div>
                    <div style="background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #334155;">
                        <strong>Agent模式</strong><br>
                        处理复杂问题和调用MCP
                    </div>
                </div>
            </div>

            <div class="highlight-box">
                <h3>🔄 工作模式区分</h3>
                <p><strong>Workflow：</strong> 遵循固定的处理流程，步骤是预先确定的</p>
                <p><strong>Agent：</strong> 根据任务动态由大模型自主决定下一步行动，具有更强的自主性和适应性</p>
            </div>

            <h3>📋 Cursor规则系统 (Rule Types)</h3>
            <div class="constraint-box" style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); border: 2px solid #64748b;">
                <h4 style="color: #475569; margin-bottom: 20px;">🔧 四种规则类型的注入时机与生效控制方式</h4>
                <div class="rule-types-table" style="overflow-x: auto; margin: 20px 0;">
                    <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 8px 25px rgba(0,0,0,0.08);">
                        <thead>
                            <tr style="background: linear-gradient(135deg, #64748b 0%, #475569 100%); color: white;">
                                <th style="padding: 15px 12px; text-align: left; font-weight: 600; border-right: 1px solid rgba(255,255,255,0.2);">📌 Rule Type</th>
                                <th style="padding: 15px 12px; text-align: left; font-weight: 600; border-right: 1px solid rgba(255,255,255,0.2);">⏰ 注入时机</th>
                                <th style="padding: 15px 12px; text-align: left; font-weight: 600;">🎛️ 生效控制方式</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="border-bottom: 1px solid #e2e8f0;">
                                <td style="padding: 15px 12px; font-weight: 600; color: #334155; border-right: 1px solid #f1f5f9;">
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <span style="background: #f59e0b; color: white; padding: 4px 8px; border-radius: 6px; font-size: 0.8rem; font-weight: 600;">Always</span>
                                    </div>
                                </td>
                                <td style="padding: 15px 12px; color: #475569; border-right: 1px solid #f1f5f9;">
                                    始终注入到模型上下文中，无论是否引用任何文件或提到它
                                </td>
                                <td style="padding: 15px 12px; color: #475569;">
                                    因为 <code style="background: #f1f5f9; padding: 2px 6px; border-radius: 4px; color: #e11d48;">alwaysApply: true</code>，始终可用
                                </td>
                            </tr>
                            <tr style="border-bottom: 1px solid #e2e8f0; background: #f8fafc;">
                                <td style="padding: 15px 12px; font-weight: 600; color: #334155; border-right: 1px solid #f1f5f9;">
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <span style="background: #10b981; color: white; padding: 4px 8px; border-radius: 6px; font-size: 0.8rem; font-weight: 600;">Auto Attached</span>
                                    </div>
                                </td>
                                <td style="padding: 15px 12px; color: #475569; border-right: 1px solid #f1f5f9;">
                                    仅当与某个 glob 模式匹配的文件被引用时注入
                                </td>
                                <td style="padding: 15px 12px; color: #475569;">
                                    靠文件路径模式决定，天然聚焦相关代码片段
                                </td>
                            </tr>
                            <tr style="border-bottom: 1px solid #e2e8f0;">
                                <td style="padding: 15px 12px; font-weight: 600; color: #334155; border-right: 1px solid #f1f5f9;">
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <span style="background: #3b82f6; color: white; padding: 4px 8px; border-radius: 6px; font-size: 0.8rem; font-weight: 600;">Agent Requested</span>
                                    </div>
                                </td>
                                <td style="padding: 15px 12px; color: #475569; border-right: 1px solid #f1f5f9;">
                                    规则始终可用，AI根据描述决定何时调用，通过 <code style="background: #dbeafe; padding: 2px 6px; border-radius: 4px; color: #1e40af;">/fetch_rules</code> 或上下文自动判断
                                </td>
                                <td style="padding: 15px 12px; color: #475569;">
                                    模型根据自然语言描述判断何时应用
                                </td>
                            </tr>
                            <tr style="background: #f8fafc;">
                                <td style="padding: 15px 12px; font-weight: 600; color: #334155; border-right: 1px solid #f1f5f9;">
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <span style="background: #6b7280; color: white; padding: 4px 8px; border-radius: 6px; font-size: 0.8rem; font-weight: 600;">Manual</span>
                                    </div>
                                </td>
                                <td style="padding: 15px 12px; color: #475569; border-right: 1px solid #f1f5f9;">
                                    不注入，也不自动激活；除非在 chat 中显式通过 <code style="background: #f3f4f6; padding: 2px 6px; border-radius: 4px; color: #374151;">@ruleName</code> 引用，才会被使用
                                </td>
                                <td style="padding: 15px 12px; color: #475569;">
                                    完全手动控制，适合法规或片段按需调用
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="rule-usage-tips" style="background: rgba(100, 116, 139, 0.1); padding: 20px; border-radius: 12px; margin-top: 25px; border-left: 4px solid #64748b;">
                    <h4 style="color: #334155; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
                        💡 规则类型使用建议
                    </h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(240px, 1fr)); gap: 15px;">
                        <div style="background: white; padding: 15px; border-radius: 8px; border-left: 3px solid #f59e0b;">
                            <strong style="color: #92400e;">Always Rules:</strong><br>
                            <span style="color: #78716c; font-size: 0.95rem;">适用于编码规范、团队约定等核心准则</span>
                        </div>
                        <div style="background: white; padding: 15px; border-radius: 8px; border-left: 3px solid #10b981;">
                            <strong style="color: #065f46;">Auto Attached:</strong><br>
                            <span style="color: #78716c; font-size: 0.95rem;">项目特定规则，自动关联相关文件</span>
                        </div>
                        <div style="background: white; padding: 15px; border-radius: 8px; border-left: 3px solid #3b82f6;">
                            <strong style="color: #1e40af;">Agent Requested:</strong><br>
                            <span style="color: #78716c; font-size: 0.95rem;">AI智能判断的专业领域规则</span>
                        </div>
                        <div style="background: white; padding: 15px; border-radius: 8px; border-left: 3px solid #6b7280;">
                            <strong style="color: #374151;">Manual:</strong><br>
                            <span style="color: #78716c; font-size: 0.95rem;">需要精确控制的特殊场景规则</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="section" id="ai-constraints">
            <h2>🎯 大模型的三重约束</h2>
            
            <div class="ai-challenges">
                <h3>制约AI应用能力的核心因素</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                    <div style="text-align: center;">
                        <div style="font-size: 2rem; margin-bottom: 10px;">🧠</div>
                        <h4>模型理解能力</h4>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 2rem; margin-bottom: 10px;">📏</div>
                        <h4>模型上下文长度</h4>
                        <p style="color: #702459; font-weight: bold;">最重要的变量</p>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 2rem; margin-bottom: 10px;">💾</div>
                        <h4>私域知识储备</h4>
                    </div>
                </div>
            </div>

            <div class="constraint-box">
                <h4>软件开发场景的特殊考虑</h4>
                <ul>
                    <li>业务代码 = 私域知识，代码量增加 > 私域知识增加</li>
                    <li>不管是RAG还是其他方式，私域知识会占据上下文长度</li>
                    <li>上下文长度越接近理论极限，理解能力衰减越快</li>
                </ul>
            </div>
            
            <h3>🏗️ AI友好的代码架构原则</h3>
            <div class="image-showcase">
                <h4 style="text-align: center; margin-bottom: 25px; color: #667eea; font-size: 1.4rem;">
                    🏗️ AI友好的代码架构设计原则
                </h4>
                <div style="text-align: center;">
                    <img src="./image/4.png" alt="AI友好性代码架构" style="max-width: 95%; border-radius: 15px; box-shadow: 0 15px 35px rgba(0,0,0,0.15);">
                    <div class="image-description" style="max-width: 700px; margin: 20px auto;">
                        🎯 采用模块化设计、清晰的命名规范、完善的文档注释，让AI更好地理解和协助开发工作
                    </div>
                </div>
            </div>
        </section>

        <section class="section" id="opportunities">
            <h2>🌊 AI时代：机遇与挑战并存</h2>
            
            <div style="background: linear-gradient(135deg, #64748b 0%, #475569 100%); color: white; padding: 40px; border-radius: 20px; margin: 30px 0; text-align: center;">
                <h3 style="color: white; font-size: 2rem; margin-bottom: 20px;">⚡ 核心观点</h3>
                <p style="font-size: 1.3rem; font-weight: 500; margin: 0;">
                    "AI淘汰的不是某个职业，而是<strong>不会使用AI的人</strong>"
                </p>
                <p style="font-size: 1.1rem; margin-top: 15px; opacity: 0.9;">
                    现在开始学习，还不算晚！关键是<strong>立即行动</strong>
                </p>
            </div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin: 40px 0;">
                <div class="highlight-box" style="background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);">
                    <h3 style="color: white;">🚀 无限机遇</h3>
                    <ul style="color: white;">
                        <li>🌍 <strong>C端场景爆发:</strong> 个人应用开发门槛大幅降低</li>
                        <li>🏢 <strong>B端重构浪潮:</strong> 所有SaaS产品都将被AI重新定义</li>
                        <li>⚡ <strong>低代码崛起:</strong> 快速工作流成为新常态</li>
                        <li>🤖 <strong>智能体定制:</strong> 基于框架的深度个性化</li>
                        <li>👤 <strong>超级个体:</strong> AI + Agents + 工作流 = 团队级生产力</li>
                    </ul>
                </div>
                
                <div class="highlight-box" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);">
                    <h3 style="color: white;">⚠️ 严峻挑战</h3>
                    <ul style="color: white;">
                        <li>❌ <strong>技能门槛提升:</strong> 入门级开发岗位将大量消失</li>
                        <li>🏗️ <strong>架构为王:</strong> 系统设计能力成为核心竞争力</li>
                        <li>👥 <strong>团队领导:</strong> 管理AI团队的能力决定上限</li>
                        <li>🧠 <strong>认知升级:</strong> 需要具备更高维度的抽象思维</li>
                        <li>⚡ <strong>适应速度:</strong> 技术迭代周期越来越短</li>
                    </ul>
                </div>
            </div>

            <h3>💪 生存与发展策略</h3>
            <div class="workflow-steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <h4>🌱 拥抱不确定性</h4>
                    <p>培养持续学习能力，在变化中寻找机会</p>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <h4>🏗️ 提升架构思维</h4>
                    <p>高维抽象能力和系统设计能力至关重要</p>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <h4>⚡ 专注力管理</h4>
                    <p>在信息爆炸时代保持深度工作状态</p>
                </div>
                <div class="step">
                    <div class="step-number">4</div>
                    <h4>🛡️ 程序鲁棒性</h4>
                    <p>编写高质量、可维护的代码变得更重要</p>
                </div>
                <div class="step">
                    <div class="step-number">5</div>
                    <h4>🎯 场景理解力</h4>
                    <p>对业务场景的深度理解比纯编码技能更重要</p>
                </div>
            </div>
        </section>

        <section class="section" id="prompt-engineering">
            <h2>🔮 提示词工程：从理论到实战</h2>
            
            <div class="workflow-steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <h4>上下文提供</h4>
                    <p>为模型设定任务的背景和范围</p>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <h4>任务定义</h4>
                    <p>告诉模型需要完成什么样的任务</p>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <h4>输出格式</h4>
                    <p>指定模型应该如何组织和呈现其输出</p>
                </div>
                <div class="step">
                    <div class="step-number">4</div>
                    <h4>行为指导</h4>
                    <p>通过提示词引导模型采取特定的思考方式</p>
                </div>
            </div>

            <h3>📋 立即可用的提示词技巧</h3>
            <div class="tips-list">
                <ul>
                    <li><strong>知识激活</strong> - "作为一名资深的XXX专家..." 唤醒模型专业知识</li>
                    <li><strong>角色扮演</strong> - 让AI扮演特定角色，获得专业视角</li>
                    <li><strong>示例驱动</strong> - 提供2-3个具体例子，让AI理解预期格式</li>
                    <li><strong>步骤分解</strong> - 将复杂任务拆分为可执行的小步骤</li>
                    <li><strong>约束明确</strong> - 设定清晰的规则和边界条件</li>
                </ul>
            </div>
            
            <div class="image-showcase">
                <h4 style="text-align: center; margin-bottom: 25px; color: #475569; font-size: 1.4rem;">
                    🎯 提示词效果对比：简单问题 vs 结构化提示
                </h4>
                <div style="text-align: center;">
                    <img src="./image/5.png" alt="提示词技巧应用效果" style="max-width: 90%; border-radius: 15px; box-shadow: 0 15px 35px rgba(0,0,0,0.15);">
                    <div class="image-description" style="max-width: 600px; margin: 20px auto; border-left: 4px solid #64748b;">
                        💡 结构化提示词能将AI回答质量提升3-5倍，从模糊建议变为可直接使用的专业方案
                    </div>
                </div>
            </div>

            <h3>📝 实战模板库</h3>
            
            <div class="image-showcase">
                <h3 style="text-align: center; margin-bottom: 40px; color: #667eea; font-size: 2rem;">
                    📊 提示词工程的实际效果对比
                </h3>
                <div class="comparison-container" style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin: 40px 0;">
                    <div class="comparison-item">
                        <div class="image-header">
                            <div class="status-indicator error"></div>
                            <h4>🤔 传统直接提问方式</h4>
                        </div>
                        <div class="image-wrapper">
                            <img src="./image/image.png" alt="直接提问效果" style="width: 100%; border-radius: 15px; box-shadow: 0 15px 35px rgba(0,0,0,0.15);">
                        </div>
                        <p class="image-description">❌ 回答模糊、不够具体、缺乏专业性</p>
                    </div>
                    <div class="comparison-item">
                        <div class="image-header">
                            <div class="status-indicator success"></div>
                            <h4>✨ 结构化提示词方式</h4>
                        </div>
                        <div class="image-wrapper">
                            <img src="./image/2.png" alt="使用提示词效果" style="width: 100%; border-radius: 15px; box-shadow: 0 15px 35px rgba(0,0,0,0.15);">
                        </div>
                        <p class="image-description">✅ 回答精准、结构清晰、专业可用</p>
                    </div>
                </div>
            </div>
            
            <h3>🌟 精心设计的提示词模板</h3>
            
            <h4>📋 双语翻译专家模板</h4>
            <div class="xml-block">
<span class="xml-tag">&lt;role&gt;</span>
<span class="xml-content">你是一位专业的中英双语翻译专家，具备深厚的语言学功底和跨文化理解能力</span>
<span class="xml-tag">&lt;/role&gt;</span>

<span class="xml-tag">&lt;task&gt;</span>
<span class="xml-content">将用户输入的文本在中英文之间进行高质量互译，确保语义准确、表达自然</span>
<span class="xml-tag">&lt;/task&gt;</span>

<span class="xml-tag">&lt;rules&gt;</span>
<span class="xml-content">  - 严格保持原文的核心意思和语气风格
  - 翻译结果要符合目标语言的表达习惯，自然流畅
  - 专业术语必须准确翻译，保持行业标准用法
  - 遇到多义词或歧义表达时，提供多种翻译选项并说明语境
  - 保留原文的格式结构和特殊标记</span>
<span class="xml-tag">&lt;/rules&gt;</span>

<span class="xml-tag">&lt;workflow&gt;</span>
<span class="xml-content">  1. 深度分析源文本的语境、文体和目标受众
  2. 识别关键术语、成语典故和文化背景元素
  3. 进行初步翻译，确保语义准确传达
  4. 优化译文表达，使其符合目标语言习惯
  5. 质量检查：术语一致性、语法正确性、流畅度
  6. 对专业术语或文化差异进行必要的解释说明</span>
<span class="xml-tag">&lt;/workflow&gt;</span>
            </div>

            <h4>📅 日期格式化函数开发模板</h4>
            <div class="xml-block">
&lt;global&gt;
$language = "JavaScript/TypeScript"
&lt;/global&gt;

&lt;role&gt;
你是一位专精于国际化和本地化开发的资深前端工程师，
特别擅长处理日期时间格式化和中华传统文化元素的技术实现
&lt;/role&gt;

&lt;task&gt;
设计并实现一个功能完备的中式日期格式化函数，
支持农历、节气、时辰等传统时间概念的现代化呈现
&lt;/task&gt;

&lt;constraints&gt;
- 输入参数: 标准JavaScript Date对象
- 开发语言: {$language}
- 函数命名: formatChineseDate
- 代码风格: 遵循ES6+规范，支持TypeScript类型定义
- 性能要求: 高效算法，支持批量处理
&lt;/constraints&gt;

&lt;output_format&gt;
完整的年月日 + 星期 + 上下午时段 + 传统时辰 格式
&lt;/output_format&gt;

&lt;examples&gt;
示例场景1 - 工作日上午:
Input: new Date('2024-01-15 09:30:00')
Output: "2024年1月15日 星期一 上午 巳时"

示例场景2 - 工作日下午:
Input: new Date('2024-01-15 13:45:00') 
Output: "2024年1月15日 星期一 下午 未时"

示例场景3 - 深夜时段:
Input: new Date('2024-01-15 23:30:00')
Output: "2024年1月15日 星期一 深夜 子时"
&lt;/examples&gt;
            </div>

            <h4>🔄 多角色分析决策模板</h4>
            <div class="xml-block">
&lt;role&gt;
  一位具备丰富软件开发、系统架构设计、需求分析经验的技术公司CTO，
  善于从多维度进行深度思考和决策分析
&lt;/role&gt;

&lt;workflow&gt;
  1. 多角色视角分析阶段：
    - 前端技术专家：关注用户体验、性能优化、开发效率
    - 后端技术专家：聚焦系统稳定性、数据处理、服务架构
    - 测试工程师：重视质量保障、风险控制、自动化测试
    - 产品经理：考虑业务价值、用户需求、市场竞争

  2. 深度分析执行步骤：
    - 阐述每个角色的专业观点和核心关注点
    - 基于角色专业知识列出关键分析论据
    - 得出符合该角色立场的明确结论

  3. 综合决策制定：
    - 对比分析各视角的异同点和冲突点
    - 整合各专业角色的核心见解和建议
    - 形成兼顾各方利益的平衡决策方案
&lt;/workflow&gt;

&lt;constraints&gt;
  - 确保每个视角的分析独立深入，避免表面化
  - 分析必须基于该角色的核心专业知识和实际关注点
  - 保持客观中立，避免主观偏见影响判断
  - 最终决策必须平衡考虑所有关键因素
&lt;/constraints&gt;

&lt;output_format&gt;
  ## 🎨 前端技术专家视角
  [用户体验、性能、开发效率等方面的专业分析]

  ## ⚙️ 后端技术专家视角  
  [系统架构、数据处理、服务稳定性等方面的深度见解]

  ## 🧪 测试工程师视角
  [质量保障、风险评估、测试策略等专业建议]

  ## 📊 产品经理视角
  [业务价值、用户需求、市场策略等产品思维]

  ## 🎯 综合决策分析
  [整合各视角见解，形成平衡的最终决策建议]
&lt;/output_format&gt;
            </div>
            
            <div class="image-showcase">
                <h4 style="text-align: center; margin-bottom: 25px; color: #667eea; font-size: 1.4rem;">
                    🎭 多角色决策分析的推理过程
                </h4>
                <div style="text-align: center;">
                    <img src="./image/3.png" alt="多角色分析推理过程" style="max-width: 85%; border-radius: 15px; box-shadow: 0 15px 35px rgba(0,0,0,0.15);">
                    <div class="image-description" style="max-width: 650px; margin: 20px auto;">
                        🧠 通过不同角色的专业视角，可以更全面地分析问题，做出更平衡的技术决策
                    </div>
                </div>
            </div>

            <h4>🔧 前端业务组件开发专家模板</h4>
            <div class="xml-block">
# Role: 前端业务组件开发专家

## Profile
- author: zhangyanhua
- version: 1.0
- language: 中文
- description: 作为一名资深的前端开发工程师，你精通现代前端开发框架和设计模式，
  能够基于最佳实践快速构建高质量、可复用的业务组件

## Goals
- 深度理解用户的业务组件需求，准确把握核心功能点
- 生成完整、规范、可维护的业务组件代码
- 确保组件设计符合前端工程化最佳实践

## Constraints
- 组件库依赖: 所有UI组件必须基于 `antd` 组件库构建
- 数据解耦原则: 
  * 严禁在组件内部直接发起网络请求
  * 所有服务端数据必须通过 props 形式传入
  * 数据变更操作通过回调函数暴露给父组件
- 样式规范: 使用 TailwindCSS 进行样式开发
- 类型安全: 完整的 TypeScript 类型定义

## Workflows
第一步: 需求分析
- 分析用户描述，明确组件的核心功能
- 识别所需的 antd 基础组件
- 确定组件的 props 接口设计

第二步: 架构设计  
- 设计组件的文件结构
- 定义 TypeScript 接口和类型
- 规划组件的状态管理逻辑

第三步: 代码实现
按照标准模板生成 5 类文件:
1. index.ts - 组件导出入口
2. interface.ts - 类型定义文件  
3. [ComponentName].tsx - 组件主体实现
4. [ComponentName].stories.tsx - Storybook 文档
5. helpers.ts - 工具函数库(如需要)

## Initialization
作为前端业务组件开发专家，我将严格遵循上述约束条件，
运用工程化思维为您构建高质量的业务组件代码。
            </div>
            
            <div class="constraint-box">
                <h4>📁 组件文件结构规范</h4>
                <div class="code-block">
├─ index.ts              // 对外导出组件
├─ interface.ts          // 定义所有类型
├─ Component.stories.tsx // Storybook文档  
├─ Component.tsx         // 主体逻辑和样式
├─ helpers.ts           // 工具函数(如有)
                </div>
                
                <h4>🔑 核心设计原则</h4>
                <ul>
                    <li>🔄 <strong>数据解耦原则:</strong> 所有服务端数据通过props传入，禁止内部请求</li>
                    <li>📦 <strong>组件库统一:</strong> 基于antd组件库，确保UI一致性</li>
                    <li>🎨 <strong>样式规范:</strong> 使用TailwindCSS，支持主题定制</li>
                    <li>📚 <strong>文档完备:</strong> 完整的Storybook文档和TypeScript类型</li>
                    <li>🧪 <strong>测试友好:</strong> 组件设计便于单元测试和集成测试</li>
                </ul>
            </div>
            
            <div class="image-showcase">
                <h4 style="text-align: center; margin-bottom: 25px; color: #667eea; font-size: 1.4rem;">
                    📦 企业级业务组件研发解决方案
                </h4>
                <div style="text-align: center;">
                    <img src="./image/8.png" alt="业务组件研发方案" style="max-width: 95%; border-radius: 15px; box-shadow: 0 15px 35px rgba(0,0,0,0.15);">
                    <div class="image-description" style="max-width: 700px; margin: 20px auto;">
                        🚀 从设计到开发，从测试到发布的完整工程化流程，确保组件质量和团队协作效率
                    </div>
                </div>
            </div>
        </section>

        <section class="section" id="learning-guide">
            <h2>🔍 如何获取AI行业信息</h2>
            
            <div class="workflow-steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <h4>理性应对信息洪流</h4>
                    <p>不要被海量信息所困扰，找到适合自己的学习节奏，保持持续学习的心态</p>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <h4>专注细分领域深耕</h4>
                    <p>选择具体的细分领域/行业持续深入，不被短期热点干扰，建立自己的知识体系</p>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <h4>重视实践应用落地</h4>
                    <p>关注实际应用场景，结合自身需求，重视实践落地而非纸上谈兵</p>
                </div>
            </div>
            
            <div class="image-showcase">
                <h4 style="text-align: center; margin-bottom: 25px; color: #667eea; font-size: 1.4rem;">
                    📚 AI学习与信息获取策略图
                </h4>
                <div style="text-align: center;">
                    <img src="./image/7.png" alt="AI学习路径规划" style="max-width: 90%; border-radius: 15px; box-shadow: 0 15px 35px rgba(0,0,0,0.15);">
                    <div class="image-description" style="max-width: 700px; margin: 20px auto;">
                        📈 系统性的学习规划和信息筛选策略，帮助开发者在AI浪潮中保持竞争优势
                    </div>
                </div>
            </div>
        </section>

        <section class="section" id="private-components">
            <h2>🏗️ 私有组件库解决方案</h2>
            
            <div class="highlight-box">
                <h3>🤔 核心问题</h3>
                <p>大模型训练数据集不包含公司私有组件数据，无法生成符合私有组件库的代码</p>
                <p style="margin-top: 15px; font-size: 1.1rem;">
                    <strong>解决问题的核心：</strong>让大模型知道你公司的私有组件库是什么样的
                </p>
            </div>

            <h3>💡 三大解决方案</h3>
            <div class="tools-grid">
                <div class="tool-card">
                    <h4>方案一：Fine-tuning</h4>
                    <p><strong>微调模型</strong><br>
                    针对特定组件库进行深度训练，让模型"学会"私有组件的使用方式<br>
                    <em>适合：大型企业，长期投入</em></p>
                </div>
                <div class="tool-card">
                    <h4>方案二：RAG</h4>
                    <p><strong>检索增强生成</strong><br>
                    动态检索组件库文档，实时为模型提供上下文信息<br>
                    <em>适合：中小型团队，快速实施</em></p>
                </div>
                <div class="tool-card">
                    <h4>方案三：预训练</h4>
                    <p><strong>基础模型增强</strong><br>
                    在现有大模型基础上进行预训练，扩展私有知识<br>
                    <em>适合：有AI技术团队的公司</em></p>
                </div>
            </div>
            
            <div class="constraint-box">
                <h4>💼 实践建议</h4>
                <ul>
                    <li><strong>起步阶段：</strong>优先选择RAG方案，投入小见效快</li>
                    <li><strong>成熟阶段：</strong>考虑Fine-tuning，提升专业化程度</li>
                    <li><strong>长远规划：</strong>结合多种方案，构建完整的AI开发生态</li>
                    <li><strong>团队协作：</strong>建立组件库文档标准，提升AI理解效果</li>
                </ul>
            </div>
        </section>

        <section class="section" id="action-guide">
            <h2>🎯 行动指南：立即开始你的AI编程之旅</h2>
            
            <div class="workflow-steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <h4>选择工具</h4>
                    <p>推荐：GitHub Copilot + Cursor<br>立即安装并开始试用</p>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <h4>学习提示词</h4>
                    <p>从模板开始，多练习<br>关键词：角色+任务+格式+约束</p>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <h4>实际项目</h4>
                    <p>选择一个小项目练手<br>重点体验AI的工作流程</p>
                </div>
                <div class="step">
                    <div class="step-number">4</div>
                    <h4>持续优化</h4>
                    <p>总结经验，优化流程<br>逐步提升AI使用效率</p>
                </div>
            </div>
            
            <div class="highlight-box">
                <h3>💡 关键要点回顾</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-top: 25px;">
                    <div style="text-align: center; padding: 15px;">
                        <div style="font-size: 2rem; margin-bottom: 10px;">🛠️</div>
                        <h4>工具先行</h4>
                        <p>选择合适的AI编程工具</p>
                    </div>
                    <div style="text-align: center; padding: 15px;">
                        <div style="font-size: 2rem; margin-bottom: 10px;">📝</div>
                        <h4>提示词工程</h4>
                        <p>掌握与AI对话的技巧</p>
                    </div>
                    <div style="text-align: center; padding: 15px;">
                        <div style="font-size: 2rem; margin-bottom: 10px;">🏗️</div>
                        <h4>架构思维</h4>
                        <p>编写AI友好的代码</p>
                    </div>
                    <div style="text-align: center; padding: 15px;">
                        <div style="font-size: 2rem; margin-bottom: 10px;">🚀</div>
                        <h4>持续学习</h4>
                        <p>保持对新技术的敏感度</p>
                    </div>
                </div>
            </div>
        </section>

        <footer class="footer">
            <h2>🌟 谢谢大家！</h2>
            <p style="font-size: 1.3rem; margin-bottom: 15px;">AI时代已来，让我们一起拥抱变化</p>
            <p style="font-size: 1.1rem; opacity: 0.9;">掌握AI，让技术为你赋能！</p>
            <br>
            <p style="opacity: 0.8;">📅 Generated from markdown content | 🚀 AI-Powered Presentation</p>
        </footer>
    </div>




</body>
</html> 