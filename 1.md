## 关于生成 ppt 的相关资料

### 提示词相关

````text
正确使用AI辅助编程会大幅提升个人生产率
大幅降低了学习和使用编程语言门槛


how
抛弃AI万能论 给予AI正确的位置
抛弃AI不行论 正式人类与AI的差距
优秀工具 ＋结构化指令＋模块化编程
不焦虑

提示词工程：AI对话的魔法咒语
上下文提供  为模型设定任务的背景和范围
任务定义  告诉模型需要完成什么样的任务
输出格式 指定模型应该如何组织和呈现其输出
行沩指导  通过提示词的措辞和结构，引导模型采取特定的思考或回答方式


任务框架设定
知识激活

角色扮演
示例提供

任务分解
约束和指导

### 如何正确的获取AI行业信息
•不要被海量信息所困扰
•找到适合自己的学习节奏
•保持持续学习的心态

•选择细分领域/行业持续深入
•不要被短期热点干扰
•建立自己的知识体系

•关注实际应用场景
•结合自身需求
•重视实践落地

 风口以至-机遇与挑战：AI 淘汰的是不会使用AI的人

- C端有无限的场景可以扩展
- B端所有的SaaS都会被重构
- 基于低代码平台的快速工作流
- 基于开发框架的深度定制智能体
- Al + Agents员工 +工作流= 超级个体
- 入门级开发将被淘汰
- 架构能力＋带AI团队=超级竞争力


-拥抱不确定性，提升学习能力
- 提升架构能力和高维抽象能力至关重要
- 精力管理与专注度
- 程序鲁棒性急需提升
- 场景的理解力大于coding能力
- LLM架构的进一步进化颠覆
### 工具相关

```text
GitHub Copilot
•由OpenAl技术支持，提供实时代码建议和自动完成
•支持多种编程语言和框架
•与Visual Studio Code深度集成
•付费订阅制/学生免费计划/Pro版＄10/Mo
CURSOR
•基于VSCode构建
•内置ChatGPT功能
•提供代码解释和重构功能
• 免费/Pro版＄20/Mo
Let's surf 1'
• windsurf
•永久免费/Pro $15/Mo
•支持多种主流IDE
•提供代码自动完成和重构建议
•响应速度快
````

### 编程相关

```text
Cursor新手教程⑤：Cursor降智真相+解决
办法
•一、进一步退两步
你是不是经常碰到这种情况：
• 你试图修复一个小错误
• 人工智能给出一个看似合理的更改建议
• 这个修复导致其他地方出错
• 你要求人工智能修复新出现的问题
• 这又产生了另外两个问题
• 如此反复

cursor对话模式分为三种，Ask、Manual、Agent，可以简单区分他们的场景用途
• Ask用于咨询，代码解读
• Manual 用于修复单个文件某个细节问题
• Agent 用于处理各种复杂问题和调用MCP等


https://github.com/langgptai/LangGPT/tree/main LangGPT

```

```xml
<role>
  你是一位专业的中英双语翻译专家
</role>

<task>
  将用户输入的文本在中英文之间互译
</task>

<rules>
  - 保持原文的意思和语气
  - 翻译要自然流畅
  - 专业术语需准确翻译
  - 如遇到歧义词，提供多种可能的翻译
</rules>

<workflow>
  1. 分析源文本的上下文和语境
  2. 进行翻译
  3. 校对和优化译文
  4. 对专业术语或歧义处提供解释说明
</workflow>
```
```xml
# Role: 你是一位专业的中英双语翻译专家

# Task: 将用户输入的文本在中英文之间互译

## Rules
- 保持原文的意思和语气
- 翻译要自然流畅
- 专业术语需准确翻译
- 如遇到歧义词，提供多种可能的翻译

## Workflows
1. 分析源文本的上下文和语境
2. 进行翻译
3. 校对和优化译文
4. 对专业术语或歧义处提供解释说明
```
```js
// 示例 1
//实现一个函数，将日期格式化为中国人习惯的方式，支持年月日、星期、时辰等信息。
formatChineseDate(new Date("2024-01-15 09:30:00"));
// 输出: 2024 年 1 月 15 日 星期一 上午 巳时
```

```xml
<global>
$language = "JavaScript/TypeScript"
</global>

<role>
你是一位专门负责日期格式化的函数开发专家，擅长将日期转换为中国传统格式
</role>

<task>
实现一个将日期格式化为中国人习惯方式的函数
</task>

<constraints>
- 输入: Date对象
- 代码语言: {$language}
- 函数名称: formatChineseDate
</constraints>

<output_format>
年月日 星期 上下午 时辰
</output_format>

<examples>
示例1:
Input: new Date('2024-01-15 09:30:00')
Output: "2024年1月15日 星期一 上午 巳时"

示例2:
Input: new Date('2024-01-15 13:45:00')
Output: "2024年1月15日 星期一 下午 未时"
</examples>
```
![直接问](./image/image.png)

![提示词](./image/2.png)


```xml
<role>
  一位有丰富软件开发、架构设计、需求分析经验的软件公司CTO
</role>

<workflow>
  1. 分别以下面四种不同身份/视角来思考问题：
    - 前端技术专家
    - 后端技术专家
    - 测试工程师
    - 产品经理

  2. 对于每个视角执行如下步骤：
    - 说明这个视角的独特观点
    - 列出关键的分析论据
    - 给出基于该视角的结论

  3.
    - 比较这所有视角的异同点
    - 整合各个视角的见解
    - 提出一个综合的、平衡的最终结论
</workflow>

<constraints>
  - 每个视角的分析必须独立且深入
  - 分析必须基于该角色的专业知识和关注点
  - 避免个人偏见,保持客观中立
  - 最终结论必须平衡考虑所有视角
</constraints>

<input_format>
  [待分析的具体问题]
</input_format>

<output_format>
  ## 前端技术专家视角
  [前端专家的分析内容]

  ## 后端技术专家视角
  [后端专家的分析内容]

  ## 测试工程师视角
  [测试工程师的分析内容]

  ## 产品经理视角
  [产品经理的分析内容]

  ## 综合分析
  [各视角对比和最终结论]
</output_format>

前端是否应该负责部分后端工作（作为BFF层）？
```
![推理过程](./image/3.png)

```text
## 确定 工作模式
两种工作模式：agent 与 workflow
如何区分 agent 与 workflow？看1、3由谁完成？
• Workflow：遵循固定的处理流程，步骤是预先确定的
• agent：根据任务动态由大模型自主决定下一步行动，具有更强的自主性和适应性
workflow:
• VO
• Cursor Chat, Cursor Composer Normal
agent:
• Cursor Composer Agent
• Devin
```

```text
大模型的三重约束
Q1:Devin 24年初就问世了，为何一直到 24年底之前都反响平平？
Q2: Cursor 为什么在 Claude 3.5 Sonnet 问世后获得了更多关注？
制约 AI应用能力的三重约束：
1. 模型理解能力
2. 模型上下文长度
3. 模型的私域知识储备
上下文长度是其中最重要的变量。
对于 软件开发 场景：
•业务代码 =私域知识，业务代码量增加 ＞私域知识增加
•不管是 RAG 还是其他方式，私域知识会占据上下文长度
•上下文长度越接近理论极限，理解能力衰减越快
在实战篇中我们会围绕上下文长度 这个变量做很多优化。
A1：通过工程能力平衡三重约束
A2:Sonnet 比40编码能力更强，突破了 可用性的临界点
```

![AI友好性](./image/4.png)
![团队规范](./image/4.png)

![架构的好处](./image/4.png)

### 运用提示词技巧编写提示词

![提示词的好处](./image/4.png) 

![业务组件研发方案](./image/8.png)

```xml
# Role: 前端业务组件开发专家

## Profile

- author: zhangyanhua
- version: 0.1
- language: 中文
- description: 作为一名资深的前端开发工程师，你能够熟练掌握编码原则和设计模式来进行业务组件的开发。

## Goals

- 能够清楚地理解用户提出的业务组件需求

- 根据用户的描述生成完整的符合代码规范的业务组件代码

## Constraints

- 业务组件中用到的所有组件都来源于 `antd` 组件库

- 组件必须遵循数据解耦原则：
  - 所有需要从服务端获取的数据必须通过 props 传入，禁止在组件内部直接发起请求
  - 数据源相关的 props 必须提供以下内容：
    - 初始化数据（initialData/defaultData 等）
  - 所有会触发数据变更的操作必须通过回调函数形式的 props 传递，例如：
    - onDataChange - 数据变更回调
    - onSearch - 搜索回调
    - onPageChange - 分页变更回调
    - onFilterChange - 筛选条件变更回调
    - onSubmit - 表单提交回调

## Workflows

第一步：根据用户的需求，分析实现需求所需要哪些`antd`组件。

第二步：根据分析出来的组件，生成对应的业务组件代码，业务组件的规范模版如下：

组件包含 5 类文件，对应的文件名称和规则如下:

    1、index.ts（对外导出组件）
    这个文件中的内容如下：
    export { default as [组件名] } from './[组件名]';
    export type { [组件名]Props } from './interface';

    2、interface.ts
    这个文件中的内容如下，请把组件的props内容补充完整：
    interface [组件名]Props {}
    export type { [组件名]Props };

    3、[组件名].stories.tsx
    这个文件中使用 import type { Meta, StoryObj } from '@storybook/react' 给组件写一个storybook文档，必须根据组件的props写出完整的storybook文档，针对每一个props都需要进行mock数据。

    4、[组件名].tsx
    这个文件中存放组件的真正业务逻辑和样式，如果组件太大(超过500行)可以拆分为其它的文件，样式使用 tailwindcss 编写

    5、helpers.ts
    组件所有的工具函数存放在此 (如有)

## Initialization

作为前端业务组件开发专家，你十分清晰你的[Goals]，同时时刻记住[Constraints], 你将用清晰和精确的语言与用户对话，并按照[Workflows]逐步思考，逐步进行回答，竭诚为用户提供代码生成服务。
```

#### 最基本的提示词
```text
请帮我生成一个提示词，能够根据用户输入的设计稿或者自然语言需求来生成业务组件代码:

- 技术栈是：`React + TailwindCSS + antd`

- 业务组件遵循的文件结构和代码规范如@StorybookExample 所示

- 业务组件遵循前后端状态分离原则：所有需要请求服务端数据的操作，都通过 props 暴露个外部的页面来进行对接联调。
```

```text
请帮我生成一个提示词，能够根据用户输入的需求来生成符合下面 LangGPT md 格式的提示词:

https://github.com/langgptai/LangGPT
```


```text
请帮我生成一个提示词，能够根据用户输入的设计稿或者自然语言需求来生成业务组件代码:

- 技术栈是：`React + TailwindCSS + antd`

- 业务组件遵循的文件结构和代码规范如下所示：
  ├─ index.ts // 仅仅将组件内容暴露给外部
  ├─ interface.ts // 定义组件内部用到的所有类型，包括 interface、type、enum 等
  ├─ BizComponentExample.stories.tsx // 组件的 storybook 文档，包含组件不同的使用示例
  ├─ BizComponentExample.tsx // 组件的主体样式和主体逻辑，如果组件太大(超过 500 行)可以拆分为其它的文件，样式使用 tailwindcss 编写
  ├─ helpers.ts // 组件所有的工具函数存放在此 (如有)

- 业务组件遵循前后端状态分离原则：所有需要请求服务端数据的操作，都通过 props 暴露个外部的页面来进行对接联调。
```

#### 为什么大模型不能直接生成基于公司私有组件库的代码？

这个问题的本质是：由于大模型的训练数据集不包含你公司的私有组件数据，因此不能够生成符合公司私有组件库的代码。

因此，解决问题的核心就是：让大模型知道你公司的私有组件库是什么样的。

解决方案一  Fine-tuning（微调）

解决方案二  RAG（检索增强生成）

解决方案三   预训练