{"cells": [{"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["<>:64: SyntaxWarning: invalid escape sequence '\\['\n", "<>:64: SyntaxWarning: invalid escape sequence '\\['\n", "/var/folders/2x/7ztnm7x5111gcvjp0x0nv6400000gn/T/ipykernel_54794/3588693233.py:64: SyntaxWarning: invalid escape sequence '\\['\n", "  text = re.sub(r'[^\\u4e00-\\u9fa5a-zA-Z0-9\\s\\.,，。！？；：\"\"''（）\\[\\]{}]', '', text)\n", "/Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages/openpyxl/styles/stylesheet.py:237: UserWarning: Workbook contains no default style, apply openpyxl's default\n", "  warn(\"Workbook contains no default style, apply openpyxl's default\")\n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== 测试修复后的ChatDoc类 ===\n", "文件 work.xlsx 存在，大小: 407609 字节\n", "=== 开始处理文档 ===\n", "成功加载 xlsx 文件，共 1 个文档片段\n", "清理后有效文档数量: 1\n", "文本分割完成，共 1 个有效片段\n", "片段 1 (长度:611479): 名称 部门 加班成员 加班时长 加班开始时间 加班结束时间 验收审核状态 验收审核通过时间 计划工作内容 实际工作内容 加班时间明细 是否提交结果物 是否确认结果物 确认加班 审批状态 加班申请 是否...\n", "\n", "=== 测试不同的查询 ===\n", "1. 测试通用查询：\n", "准备向量化 1 个文本片段\n", "片段 1 内容预览: '名称 部门 加班成员 加班时长 加班开始时间 加班结束时间 验收审核状态 验收审核通过时间 计划工作...'\n", "测试向量化第一个片段: '名称 部门 加班成员 加班时长 加班开始时间 加班结束时间 验收审核状态 验收审核通过时间 计划工作...'\n", "向量化失败: Error code: 400 - {'code': 20015, 'message': 'The parameter is invalid. Please check again.', 'data': None}\n", "错误类型: BadRequestError\n", "简单文本向量化成功，问题可能在于文档内容\n", "向量数据库创建失败\n", "结果数量: 0\n", "\n", "2. 测试原始查询：\n", "准备向量化 1 个文本片段\n", "片段 1 内容预览: '名称 部门 加班成员 加班时长 加班开始时间 加班结束时间 验收审核状态 验收审核通过时间 计划工作...'\n", "测试向量化第一个片段: '名称 部门 加班成员 加班时长 加班开始时间 加班结束时间 验收审核状态 验收审核通过时间 计划工作...'\n", "向量化失败: Error code: 400 - {'code': 20015, 'message': 'The parameter is invalid. Please check again.', 'data': None}\n", "错误类型: BadRequestError\n", "简单文本向量化成功，问题可能在于文档内容\n", "向量数据库创建失败\n", "结果数量: 0\n", "没有找到相关数据\n"]}], "source": ["# 测试修复后的ChatDoc类\n", "from langchain_community.document_loaders import UnstructuredExcelLoader,Docx2txtLoader, PyPDFLoader\n", "from langchain_text_splitters import CharacterTextSplitter \n", "from langchain_chroma import Chroma \n", "from langchain_openai import OpenAIEmbeddings \n", "from langchain.retrievers.multi_query import MultiQueryRetriever\n", "import os\n", "import re\n", "from langchain_deepseek import ChatDeepSeek\n", "from langchain.retrievers import ContextualCompressionRetriever\n", "from langchain.retrievers.document_compressors import LLMChainExtractor\n", "\n", "# 使用国产模型\n", "embeddings_model = OpenAIEmbeddings(model=\"BAAI/bge-m3\",api_key=\"sk-vgbdlqjvfytxctphzbbdzcfzzqhpmrjhjynftgnpdkchbjfd\",base_url=\"https://api.siliconflow.cn/v1\")\n", "\n", "llm = ChatDeepSeek(\n", "    model=\"deepseek-chat\",\n", "    temperature=0,\n", "    api_key=\"sk-778deaff83d44d3a864caa8f3cf46c75\",\n", "    base_url=\"https://api.deepseek.com/v1\"\n", ")\n", "\n", "# 定义修复后的ChatDoc类\n", "class ChatDoc():\n", "    def __init__(self):\n", "        self.doc = None\n", "        self.splitText = []\n", "        self.file_path = None\n", "\n", "    def getFile(self):\n", "        doc = self.doc\n", "        if not doc:\n", "            print(\"错误：未设置文档路径\")\n", "            return None\n", "            \n", "        loaders = {\n", "            \"docx\":Docx2txtLoader,\n", "            \"pdf\":<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "            \"xlsx\":UnstructuredExcelLoader,\n", "        }\n", "        file_extension = doc.split(\".\")[-1]\n", "        loader_class = loaders.get(file_extension)\n", "        if loader_class:\n", "            try:\n", "                loader = loader_class(doc)\n", "                text = loader.load()\n", "                print(f\"成功加载 {file_extension} 文件，共 {len(text)} 个文档片段\")\n", "                return text\n", "            except Exception as e:\n", "                print(f\"Error loading {file_extension} files:{e}\")\n", "                return None\n", "        else:\n", "            print(f\"Unsupported file extension: {file_extension}\")\n", "            return None\n", "    \n", "    # 清理文本内容\n", "    def clean_text(self, text):\n", "        \"\"\"清理文本，去除特殊字符和多余空格\"\"\"\n", "        if not text or not text.strip():\n", "            return \"\"\n", "        # 去除多余的空格和换行\n", "        text = re.sub(r'\\s+', ' ', text.strip())\n", "        # 去除特殊字符，保留中文、英文、数字和基本标点\n", "        text = re.sub(r'[^\\u4e00-\\u9fa5a-zA-Z0-9\\s\\.,，。！？；：\"\"''（）\\[\\]{}]', '', text)\n", "        return text\n", "    \n", "    # 分割文本\n", "    def splitSentence(self):\n", "        full_text = self.getFile()\n", "        if full_text != None:\n", "            # 先清理和验证文本\n", "            cleaned_docs = []\n", "            for doc in full_text:\n", "                cleaned_content = self.clean_text(doc.page_content)\n", "                if cleaned_content and len(cleaned_content.strip()) > 10:  # 确保文本有意义\n", "                    doc.page_content = cleaned_content\n", "                    cleaned_docs.append(doc)\n", "            \n", "            if not cleaned_docs:\n", "                print(\"错误：清理后没有有效的文本内容\")\n", "                return\n", "            \n", "            print(f\"清理后有效文档数量: {len(cleaned_docs)}\")\n", "            \n", "            # 调整分割参数，增加chunk_size\n", "            text_splitter = CharacterTextSplitter(chunk_size=500, chunk_overlap=50)\n", "            texts = text_splitter.split_documents(cleaned_docs)\n", "            \n", "            # 再次过滤太短的片段\n", "            valid_texts = [text for text in texts if len(text.page_content.strip()) > 20]\n", "            \n", "            self.splitText = valid_texts\n", "            print(f\"文本分割完成，共 {len(valid_texts)} 个有效片段\")\n", "            \n", "            # 打印前几个片段的内容用于调试\n", "            for i, text in enumerate(valid_texts[:3]):\n", "                print(f\"片段 {i+1} (长度:{len(text.page_content)}): {text.page_content[:100]}...\")\n", "        else:\n", "            print(\"文件加载失败\")\n", "\n", "    def getSplitText(self):\n", "        return self.splitText\n", "    \n", "    #向量化向量存贮\n", "    def embeddingAmdVectorDB(self):\n", "        if not self.splitText:\n", "            print(\"错误：没有分割的文本数据\")\n", "            return None\n", "        \n", "        # 验证文本内容\n", "        print(f\"准备向量化 {len(self.splitText)} 个文本片段\")\n", "        for i, doc in enumerate(self.splitText[:2]):\n", "            print(f\"片段 {i+1} 内容预览: '{doc.page_content[:50]}...'\")\n", "        \n", "        try:\n", "            # 测试单个文本的向量化\n", "            test_texts = [doc.page_content for doc in self.splitText[:1]]\n", "            print(f\"测试向量化第一个片段: '{test_texts[0][:50]}...'\")\n", "            \n", "            # 先测试embedding是否工作\n", "            test_embedding = embeddings_model.embed_documents(test_texts)\n", "            print(f\"测试向量化成功，向量维度: {len(test_embedding[0])}\")\n", "            \n", "            # 如果测试成功，创建完整的向量数据库\n", "            db = Chroma.from_documents(self.splitText, embeddings_model)\n", "            print(\"向量数据库创建成功\")\n", "            return db\n", "        except Exception as e:\n", "            print(f\"向量化失败: {e}\")\n", "            print(f\"错误类型: {type(e).__name__}\")\n", "            # 尝试使用更简单的文本\n", "            try:\n", "                simple_text = \"测试文本\"\n", "                test_embedding = embeddings_model.embed_documents([simple_text])\n", "                print(\"简单文本向量化成功，问题可能在于文档内容\")\n", "            except Exception as e2:\n", "                print(f\"连简单文本都无法向量化: {e2}\")\n", "            return None\n", "    \n", "    # 向量化\n", "    def embedding(self):\n", "        return embeddings_model.embed_documents(self.splitText)\n", "    \n", "    # 向量检索\n", "    def vectorSearch(self,query,k=5):\n", "        db = self.embeddingAmdVectorDB()\n", "        if db:\n", "            results = db.similarity_search(query,k)\n", "            print(f\"基础向量检索找到 {len(results)} 个相关片段\")\n", "            return results\n", "        return []\n", "    \n", "    #提问并找到相关文本快，使用压缩检索\n", "    def askANdFindFiles(self,query):\n", "        db = self.embeddingAmdVectorDB()\n", "        if not db:\n", "            print(\"向量数据库创建失败\")\n", "            return []\n", "            \n", "        try:\n", "            # 先尝试基础检索看看是否有结果\n", "            basic_results = db.similarity_search(query, k=5)\n", "            print(f\"基础检索找到 {len(basic_results)} 个相关片段\")\n", "            \n", "            if not basic_results:\n", "                print(\"基础检索没有找到相关内容，请检查查询词是否存在于文档中\")\n", "                return []\n", "            \n", "            # 创建上下文压缩检索 - 修复参数名\n", "            retriever = db.as_retriever()\n", "            compressor = LLMChainExtractor.from_llm(llm=llm)\n", "            compressed_retriever = ContextualCompressionRetriever(\n", "                base_retriever=retriever,\n", "                base_compressor=compressor,  # 修复：使用正确的参数名\n", "            )\n", "            results = compressed_retriever.invoke(query)\n", "            print(f\"压缩检索找到 {len(results)} 个相关片段\")\n", "            return results\n", "            \n", "        except Exception as e:\n", "            print(f\"检索过程出错: {e}\")\n", "            # 如果压缩检索失败，返回基础检索结果\n", "            return self.vectorSearch(query)\n", "\n", "if __name__ == \"__main__\":\n", "    # 测试修复后的代码\n", "    print(\"=== 测试修复后的ChatDoc类 ===\")\n", "    \n", "    # 首先检查文件是否存在\n", "    import os\n", "    file_path = \"work.xlsx\"\n", "    if not os.path.exists(file_path):\n", "        print(f\"错误：文件 {file_path} 不存在\")\n", "        print(\"请确保Excel文件在当前目录下\")\n", "    else:\n", "        print(f\"文件 {file_path} 存在，大小: {os.path.getsize(file_path)} 字节\")\n", "    \n", "    chat_doc = ChatDoc()\n", "    chat_doc.doc = \"work.xlsx\"\n", "    print(\"=== 开始处理文档 ===\")\n", "    chat_doc.splitSentence()\n", "\n", "    print(\"\\n=== 测试不同的查询 ===\")\n", "\n", "    # 先测试一个更通用的查询\n", "    print(\"1. 测试通用查询：\")\n", "    unique_doc = chat_doc.askANdFindFiles(\"请假\")\n", "    print(f\"结果数量: {len(unique_doc)}\")\n", "    for i, doc in enumerate(unique_doc[:2]):\n", "        print(f\"结果 {i+1}: {doc.page_content[:200]}...\")\n", "\n", "    print(\"\\n2. 测试原始查询：\")\n", "    unique_doc = chat_doc.askANdFindFiles(\"我想查叶康杰的加班记录\")\n", "    print(f\"结果数量: {len(unique_doc)}\")\n", "    if unique_doc:\n", "        for i, doc in enumerate(unique_doc):\n", "            print(f\"结果 {i+1}: {doc.page_content[:200]}...\")\n", "    else:\n", "        print(\"没有找到相关数据\") "]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 检查Excel文件内容 ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/ls/envs/tushare/lib/python3.13/site-packages/openpyxl/styles/stylesheet.py:237: UserWarning: Workbook contains no default style, apply openpyxl's default\n", "  warn(\"Workbook contains no default style, apply openpyxl's default\")\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Excel文件信息:\n", "- 行数: 1979\n", "- 列数: 24\n", "- 列名: ['名称', '部门', '加班成员', '加班时长', '加班开始时间', '加班结束时间', '验收审核状态', '验收审核通过时间', '计划工作内容', '实际工作内容', '加班时间明细', '是否提交结果物', '是否确认结果物', '确认加班', '审批状态', '加班申请', '是否出差', '工作截图', '项目角色', '产品项目交付小组', '商机项目交付小组', '创建人', '创建时间', 'ID']\n", "- 前3行数据:\n", "                                   名称      部门 加班成员  加班时长               加班开始时间               加班结束时间 验收审核状态 验收审核通过时间                                                                         计划工作内容 实际工作内容                                                                         加班时间明细 是否提交结果物 是否确认结果物 确认加班 审批状态         加班申请 是否出差 工作截图  项目角色 产品项目交付小组                   商机项目交付小组 创建人                 创建时间          ID\n", "0  于续军-2024年新疆自治区高效办成一件事商机项目交付小组-2327  交付实施二部  于续军  16.0  2025-06-14 00:00:00  2025-06-15 00:00:00    NaN      NaN  1、25年已上线12个自查及问题整理；\\n2、12个一件事一件事自查问题整改及办事指南在线文档调整\\n3、新上线5个一件事表单、办事指南、解读配置优化调整    NaN  [{\"date\":\"2025-06-14\",\"workHours\":\"8\"},{\"date\":\"2025-06-15\",\"workHours\":\"8\"}]     NaN     NaN  NaN  审批中  张辉-发起加班1400    是  NaN  交付人员      NaN  2024年新疆自治区高效办成一件事商机项目交付小组  张辉  2025-06-15 10:49:44  KQM3171652\n", "1  李星宇-2024年新疆自治区高效办成一件事商机项目交付小组-2326  交付实施二部  李星宇   8.0  2025-06-14 00:00:00  2025-06-15 00:00:00    NaN      NaN                                              1、新上线一件事这件事解读、办事指南调整\\n2、巡检并编写运维报告    NaN                                        [{\"date\":\"2025-06-14\",\"workHours\":\"8\"}]     NaN     NaN  NaN  审批中  张辉-发起加班1400    否  NaN  交付人员      NaN  2024年新疆自治区高效办成一件事商机项目交付小组  张辉  2025-06-15 10:49:44  KQM3171651\n", "2  向宇泉-2024年新疆自治区高效办成一件事商机项目交付小组-2325  交付实施二部  向宇泉   4.0  2025-06-14 00:00:00  2025-06-15 00:00:00    NaN      NaN                                                             新上线一件事这件事解读、办事指南调整    NaN                                        [{\"date\":\"2025-06-14\",\"workHours\":\"4\"}]     NaN     NaN  NaN  审批中  张辉-发起加班1400    否  NaN  交付人员      NaN  2024年新疆自治区高效办成一件事商机项目交付小组  张辉  2025-06-15 10:49:44  KQM3171630\n", "- 在列 '名称' 中找到 '叶康杰'\n", "  匹配的行数: 2\n", "- 在列 '加班成员' 中找到 '叶康杰'\n", "  匹配的行数: 2\n"]}], "source": ["# 检查Excel文件内容的辅助代码\n", "import pandas as pd\n", "import os\n", "\n", "def check_excel_content(file_path):\n", "    \"\"\"检查Excel文件的实际内容\"\"\"\n", "    if not os.path.exists(file_path):\n", "        print(f\"文件 {file_path} 不存在\")\n", "        return\n", "    \n", "    try:\n", "        # 使用pandas读取Excel文件\n", "        df = pd.read_excel(file_path)\n", "        print(f\"Excel文件信息:\")\n", "        print(f\"- 行数: {len(df)}\")\n", "        print(f\"- 列数: {len(df.columns)}\")\n", "        print(f\"- 列名: {list(df.columns)}\")\n", "        \n", "        if len(df) > 0:\n", "            print(f\"- 前3行数据:\")\n", "            print(df.head(3).to_string())\n", "        else:\n", "            print(\"- 文件只有表头，没有数据行\")\n", "            \n", "        # 检查是否有包含\"叶康杰\"的数据\n", "        if len(df) > 0:\n", "            for col in df.columns:\n", "                if df[col].astype(str).str.contains('叶康杰', na=False).any():\n", "                    print(f\"- 在列 '{col}' 中找到 '叶康杰'\")\n", "                    matching_rows = df[df[col].astype(str).str.contains('叶康杰', na=False)]\n", "                    print(f\"  匹配的行数: {len(matching_rows)}\")\n", "                    \n", "    except Exception as e:\n", "        print(f\"读取Excel文件失败: {e}\")\n", "\n", "# 检查work.xlsx文件\n", "print(\"=== 检查Excel文件内容 ===\")\n", "check_excel_content(\"work.xlsx\")\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 测试向量化API ===\n", "测试文本 1: '这是一个测试文本'\n", "  ✓ 成功，向量维度: 1024\n", "测试文本 2: '叶康杰的加班记录'\n", "  ✓ 成功，向量维度: 1024\n", "测试文本 3: '员工姓名 部门 工作时间'\n", "  ✓ 成功，向量维度: 1024\n", "\n", "=== 测试完成 ===\n"]}], "source": ["# 测试向量化API是否正常工作\n", "from langchain_openai import OpenAIEmbeddings\n", "\n", "print(\"=== 测试向量化API ===\")\n", "\n", "# 使用相同的配置\n", "embeddings_model = OpenAIEmbeddings(\n", "    model=\"BAAI/bge-m3\",\n", "    api_key=\"sk-vgbdlqjvfytxctphzbbdzcfzzqhpmrjhjynftgnpdkchbjfd\",\n", "    base_url=\"https://api.siliconflow.cn/v1\"\n", ")\n", "\n", "# 测试简单文本\n", "test_texts = [\n", "    \"这是一个测试文本\",\n", "    \"叶康杰的加班记录\",\n", "    \"员工姓名 部门 工作时间\"\n", "]\n", "\n", "for i, text in enumerate(test_texts):\n", "    try:\n", "        print(f\"测试文本 {i+1}: '{text}'\")\n", "        embedding = embeddings_model.embed_documents([text])\n", "        print(f\"  ✓ 成功，向量维度: {len(embedding[0])}\")\n", "    except Exception as e:\n", "        print(f\"  ✗ 失败: {e}\")\n", "        \n", "print(\"\\n=== 测试完成 ===\")\n"]}], "metadata": {"kernelspec": {"display_name": "tushare", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}